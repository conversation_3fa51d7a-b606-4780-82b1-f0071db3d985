import { v4 as uuidv4 } from 'uuid';
import { CatalystIntentAnalyzer } from './intent-analyzer.js';
import { CatalystContextManager } from './context-manager.js';
import { CatalystRoutingEngine } from './routing-engine.js';
import { CatalystOptimizationCoordinator } from './optimization-coordinator.js';
import { CatalystLearningModule } from './learning-module.js';
import type {
  IntentAnalyzer,
  ContextManager,
  RoutingEngine,
  OptimizationCoordinator,
  LearningModule,
  Intent,
  Context,
  ProcessingResult,
  LearningData,
  RouteConfig,
} from './types.js';

export interface CatalystCoreOptions {
  maxContextHistory?: number;
  contextTTL?: number;
  maxLearningData?: number;
  enableLearning?: boolean;
  enableOptimization?: boolean;
}

export class CatalystCore {
  private intentAnalyzer: IntentAnalyzer;
  private contextManager: ContextManager;
  private routingEngine: RoutingEngine;
  private optimizationCoordinator: OptimizationCoordinator;
  private learningModule: LearningModule;
  
  private enableLearning: boolean;
  private enableOptimization: boolean;

  constructor(options: CatalystCoreOptions = {}) {
    // Initialize components
    this.intentAnalyzer = new CatalystIntentAnalyzer();
    this.contextManager = new CatalystContextManager({
      maxHistorySize: options.maxContextHistory,
      contextTTL: options.contextTTL,
    });
    this.routingEngine = new CatalystRoutingEngine();
    this.optimizationCoordinator = new CatalystOptimizationCoordinator();
    this.learningModule = new CatalystLearningModule({
      maxDataPoints: options.maxLearningData,
    });

    // Configuration
    this.enableLearning = options.enableLearning ?? true;
    this.enableOptimization = options.enableOptimization ?? true;
  }

  /**
   * Process a prompt through the complete Catalyst pipeline
   */
  async process(
    prompt: string,
    sessionId: string,
    userId?: string,
    contextId?: string
  ): Promise<ProcessingResult> {
    const startTime = new Date();

    try {
      // Step 1: Get or create context
      let context: Context;
      if (contextId) {
        const existingContext = await this.contextManager.getContext(contextId);
        if (existingContext) {
          context = existingContext;
        } else {
          context = this.contextManager.createContext(sessionId, userId);
        }
      } else {
        context = this.contextManager.createContext(sessionId, userId);
      }

      // Step 2: Analyze intent
      const intent = await this.intentAnalyzer.analyze(prompt, context);

      // Step 3: Find best route
      const route = await this.routingEngine.findBestRoute(intent);
      if (!route) {
        throw new Error('No suitable route found for the given intent');
      }

      // Step 4: Optimize intent (if enabled)
      let optimizedIntent = intent;
      if (this.enableOptimization) {
        optimizedIntent = await this.optimizationCoordinator.optimize(intent, route);
      }

      // Step 5: Process the request (placeholder - would integrate with actual AI models)
      const result = await this.executeRoute(optimizedIntent, route);

      // Step 6: Update context with the interaction
      await this.contextManager.addToHistory(context.id, {
        intent: optimizedIntent,
        route: route.id,
        result,
        timestamp: new Date(),
      });

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      const processingResult: ProcessingResult = {
        id: uuidv4(),
        intentId: optimizedIntent.id,
        routeId: route.id,
        result,
        metadata: {
          contextId: context.id,
          optimized: this.enableOptimization,
          routeName: route.name,
        },
        performance: {
          startTime,
          endTime,
          duration,
          tokensUsed: this.estimateTokenUsage(optimizedIntent.prompt, result),
        },
        success: true,
      };

      // Step 7: Learn from the interaction (if enabled)
      if (this.enableLearning) {
        await this.learn(processingResult);
      }

      return processingResult;

    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      return {
        id: uuidv4(),
        intentId: 'unknown',
        routeId: 'unknown',
        result: null,
        performance: {
          startTime,
          endTime,
          duration,
        },
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Add a custom route to the routing engine
   */
  addRoute(route: RouteConfig): void {
    this.routingEngine.addRoute(route);
  }

  /**
   * Remove a route from the routing engine
   */
  removeRoute(routeId: string): void {
    this.routingEngine.removeRoute(routeId);
  }

  /**
   * Get insights for a specific intent type
   */
  async getInsights(intentType: Parameters<LearningModule['getInsights']>[0]) {
    return this.learningModule.getInsights(intentType);
  }

  /**
   * Get performance analysis for recent results
   */
  async analyzePerformance(results: ProcessingResult[]) {
    return this.optimizationCoordinator.analyzePerformance(results);
  }

  /**
   * Get suggestions for improving a result
   */
  async getSuggestions(result: ProcessingResult) {
    return this.optimizationCoordinator.suggestImprovements(result);
  }

  /**
   * Get system statistics
   */
  async getStats() {
    const contextStats = await this.contextManager.getContextStats();
    const learningStats = this.learningModule.getLearningStats();
    const routes = this.routingEngine.getAllRoutes();

    return {
      contexts: contextStats,
      learning: learningStats,
      routes: {
        total: routes.length,
        active: routes.filter(r => r.priority > 0).length,
      },
      system: {
        learningEnabled: this.enableLearning,
        optimizationEnabled: this.enableOptimization,
      },
    };
  }

  /**
   * Export learning data for backup or analysis
   */
  async exportLearningData() {
    return this.learningModule.exportLearningData();
  }

  /**
   * Import learning data from backup
   */
  async importLearningData(data: LearningData[]) {
    return this.learningModule.importLearningData(data);
  }

  /**
   * Clear all learning data
   */
  async clearLearningData() {
    return this.learningModule.clearLearningData();
  }

  // Private methods

  private async executeRoute(intent: Intent, route: RouteConfig): Promise<unknown> {
    // This is a placeholder for actual AI model integration
    // In a real implementation, this would:
    // 1. Format the prompt according to the model's requirements
    // 2. Make API calls to the specified model
    // 3. Handle responses and errors
    // 4. Return the processed result

    // Simulate processing time based on route complexity
    const processingTime = Math.random() * 2000 + 500; // 500-2500ms
    await new Promise(resolve => setTimeout(resolve, processingTime));

    // Mock response based on intent type
    switch (intent.type) {
      case 'generate':
        return {
          type: 'generation',
          content: `Generated content for: ${intent.prompt.substring(0, 50)}...`,
          model: route.modelConfig.model,
        };
      
      case 'analyze':
        return {
          type: 'analysis',
          insights: ['Key insight 1', 'Key insight 2', 'Key insight 3'],
          confidence: 0.85,
          model: route.modelConfig.model,
        };
      
      case 'optimize':
        return {
          type: 'optimization',
          original: intent.prompt,
          optimized: `Optimized version of: ${intent.prompt}`,
          improvements: ['Clarity improved', 'Conciseness enhanced'],
          model: route.modelConfig.model,
        };
      
      default:
        return {
          type: 'generic',
          result: `Processed ${intent.type} request`,
          model: route.modelConfig.model,
        };
    }
  }

  private async learn(result: ProcessingResult): Promise<void> {
    // Calculate performance score based on various factors
    const performance = this.calculatePerformanceScore(result);

    const learningData: LearningData = {
      intentId: result.intentId,
      routeId: result.routeId,
      performance,
      timestamp: new Date(),
      metadata: result.metadata,
    };

    await this.learningModule.learn(learningData);

    // Update route weights in the routing engine
    if (this.routingEngine instanceof CatalystRoutingEngine) {
      (this.routingEngine as any).recordRoutePerformance(result.routeId, performance);
    }
  }

  private calculatePerformanceScore(result: ProcessingResult): number {
    let score = 0.5; // Base score

    // Success factor
    if (result.success) {
      score += 0.3;
    }

    // Duration factor (faster is better, normalized to 30 seconds)
    const durationScore = Math.max(0, 1 - (result.performance.duration / 30000));
    score += durationScore * 0.2;

    // Token efficiency (if available)
    if (result.performance.tokensUsed) {
      const tokenEfficiency = Math.max(0, 1 - (result.performance.tokensUsed / 4000));
      score += tokenEfficiency * 0.1;
    }

    return Math.min(score, 1.0);
  }

  private estimateTokenUsage(prompt: string, result: unknown): number {
    // Simple token estimation (roughly 4 characters per token)
    const promptTokens = Math.ceil(prompt.length / 4);
    const resultTokens = Math.ceil(JSON.stringify(result).length / 4);
    return promptTokens + resultTokens;
  }
}
