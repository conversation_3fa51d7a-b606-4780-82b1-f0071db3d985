import { v4 as uuidv4 } from 'uuid';
import type { ContextManager, Context } from './types.js';

export class CatalystContextManager implements ContextManager {
  private contexts: Map<string, Context> = new Map();
  private maxHistorySize: number = 100;
  private contextTTL: number = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor(options?: { maxHistorySize?: number; contextTTL?: number }) {
    if (options?.maxHistorySize) {
      this.maxHistorySize = options.maxHistorySize;
    }
    if (options?.contextTTL) {
      this.contextTTL = options.contextTTL;
    }

    // Start cleanup interval
    this.startCleanupInterval();
  }

  createContext(sessionId: string, userId?: string): Context {
    const context: Context = {
      id: uuidv4(),
      userId,
      sessionId,
      timestamp: new Date(),
      metadata: {},
      history: [],
    };

    this.contexts.set(context.id, context);
    return context;
  }

  async updateContext(
    contextId: string,
    updates: Partial<Context>
  ): Promise<Context> {
    const context = this.contexts.get(contextId);
    if (!context) {
      throw new Error(`Context with id ${contextId} not found`);
    }

    const updatedContext: Context = {
      ...context,
      ...updates,
      id: context.id, // Ensure ID cannot be changed
      timestamp: new Date(), // Update timestamp
    };

    this.contexts.set(contextId, updatedContext);
    return updatedContext;
  }

  async getContext(contextId: string): Promise<Context | null> {
    const context = this.contexts.get(contextId);
    
    if (!context) {
      return null;
    }

    // Check if context has expired
    const now = new Date().getTime();
    const contextTime = context.timestamp.getTime();
    
    if (now - contextTime > this.contextTTL) {
      this.contexts.delete(contextId);
      return null;
    }

    return context;
  }

  async addToHistory(contextId: string, entry: unknown): Promise<void> {
    const context = await this.getContext(contextId);
    if (!context) {
      throw new Error(`Context with id ${contextId} not found`);
    }

    if (!context.history) {
      context.history = [];
    }

    // Add entry with timestamp
    const historyEntry = {
      timestamp: new Date(),
      data: entry,
    };

    context.history.push(historyEntry);

    // Trim history if it exceeds max size
    if (context.history.length > this.maxHistorySize) {
      context.history = context.history.slice(-this.maxHistorySize);
    }

    // Update the context
    await this.updateContext(contextId, { history: context.history });
  }

  async clearHistory(contextId: string): Promise<void> {
    const context = await this.getContext(contextId);
    if (!context) {
      throw new Error(`Context with id ${contextId} not found`);
    }

    await this.updateContext(contextId, { history: [] });
  }

  // Additional utility methods
  async getContextsBySession(sessionId: string): Promise<Context[]> {
    const contexts: Context[] = [];
    
    for (const context of this.contexts.values()) {
      if (context.sessionId === sessionId) {
        // Check if context is still valid
        const validContext = await this.getContext(context.id);
        if (validContext) {
          contexts.push(validContext);
        }
      }
    }

    return contexts;
  }

  async getContextsByUser(userId: string): Promise<Context[]> {
    const contexts: Context[] = [];
    
    for (const context of this.contexts.values()) {
      if (context.userId === userId) {
        // Check if context is still valid
        const validContext = await this.getContext(context.id);
        if (validContext) {
          contexts.push(validContext);
        }
      }
    }

    return contexts;
  }

  async deleteContext(contextId: string): Promise<boolean> {
    return this.contexts.delete(contextId);
  }

  async getContextStats(): Promise<{
    totalContexts: number;
    activeContexts: number;
    averageHistorySize: number;
  }> {
    const totalContexts = this.contexts.size;
    let activeContexts = 0;
    let totalHistorySize = 0;

    const now = new Date().getTime();

    for (const context of this.contexts.values()) {
      const contextTime = context.timestamp.getTime();
      
      if (now - contextTime <= this.contextTTL) {
        activeContexts++;
        totalHistorySize += context.history?.length || 0;
      }
    }

    return {
      totalContexts,
      activeContexts,
      averageHistorySize: activeContexts > 0 ? totalHistorySize / activeContexts : 0,
    };
  }

  private startCleanupInterval(): void {
    // Run cleanup every hour
    setInterval(() => {
      this.cleanupExpiredContexts();
    }, 60 * 60 * 1000);
  }

  private cleanupExpiredContexts(): void {
    const now = new Date().getTime();
    const expiredContextIds: string[] = [];

    for (const [contextId, context] of this.contexts) {
      const contextTime = context.timestamp.getTime();
      
      if (now - contextTime > this.contextTTL) {
        expiredContextIds.push(contextId);
      }
    }

    for (const contextId of expiredContextIds) {
      this.contexts.delete(contextId);
    }

    if (expiredContextIds.length > 0) {
      console.log(`Cleaned up ${expiredContextIds.length} expired contexts`);
    }
  }

  // Method to serialize context for storage
  serializeContext(context: Context): string {
    return JSON.stringify({
      ...context,
      timestamp: context.timestamp.toISOString(),
    });
  }

  // Method to deserialize context from storage
  deserializeContext(serialized: string): Context {
    const parsed = JSON.parse(serialized);
    return {
      ...parsed,
      timestamp: new Date(parsed.timestamp),
    };
  }
}
