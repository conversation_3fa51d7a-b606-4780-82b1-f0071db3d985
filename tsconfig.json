{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@catalyst/core": ["./packages/catalyst-core/src"], "@catalyst/promptspec": ["./packages/promptspec/src"], "@catalyst/prompt4me": ["./packages/prompt4me/src"], "@catalyst/turbines": ["./packages/turbines/src"], "@catalyst/forge": ["./packages/forge/src"], "@catalyst/synth": ["./packages/synth/src"], "@catalyst/alchemy": ["./packages/alchemy/src"], "@catalyst/portals": ["./packages/portals/src"], "@catalyst/openrouter-adapter": ["./packages/openrouter-adapter/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}