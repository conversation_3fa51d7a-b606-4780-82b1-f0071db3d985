import { describe, it, expect, beforeEach } from '@jest/globals';
import { CatalystPromptSpecCompiler } from './compiler.js';
import {
  createPromptSpec,
  createPromptTemplate,
  createPrompt<PERSON>hain,
  createParameter,
  createModelConfig,
} from './utils.js';
import type { PromptSpec, CompilationOptions } from './types.js';

describe('CatalystPromptSpecCompiler', () => {
  let compiler: CatalystPromptSpecCompiler;
  let sampleSpec: PromptSpec;

  beforeEach(() => {
    compiler = new CatalystPromptSpecCompiler();
    
    // Create a sample spec for testing
    sampleSpec = createPromptSpec('Test Spec', 'A test specification');
    
    const template = createPromptTemplate(
      'Greeting Template',
      'user',
      'Hello {{name}}, welcome to {{platform}}!',
      {
        description: 'A simple greeting template',
        parameters: [
          createParameter('name', 'string', {
            description: 'User name',
            required: true,
          }),
          createParameter('platform', 'string', {
            description: 'Platform name',
            required: true,
            default: 'Catalyst',
          }),
        ],
        modelConfig: createModelConfig('openai', 'gpt-4o'),
      }
    );

    sampleSpec.templates.push(template);

    const chain = createPromptChain('Welcome Chain', 'A welcome workflow');
    chain.steps.push({
      id: 'step-1',
      name: 'Greet User',
      templateId: template.id,
      inputMapping: {
        name: 'user.name',
        platform: 'config.platform',
      },
    });

    sampleSpec.chains.push(chain);
  });

  describe('compile', () => {
    it('should compile to YAML format', async () => {
      const options: CompilationOptions = {
        format: 'yaml',
        includeMetadata: true,
        includeTests: true,
        includeValidation: true,
        validate: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(result.format).toBe('yaml');
      expect(result.output).toContain('specVersion: "2.0"');
      expect(result.output).toContain('name: Test Spec');
      expect(result.metadata.templateCount).toBe(1);
      expect(result.metadata.chainCount).toBe(1);
    });

    it('should compile to JSON format', async () => {
      const options: CompilationOptions = {
        format: 'json',
        includeMetadata: true,
        validate: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(result.format).toBe('json');
      
      // Should be valid JSON
      expect(() => JSON.parse(result.output!)).not.toThrow();
      
      const parsed = JSON.parse(result.output!);
      expect(parsed.specVersion).toBe('2.0');
      expect(parsed.metadata.name).toBe('Test Spec');
    });

    it('should compile to LaTeX format', async () => {
      const options: CompilationOptions = {
        format: 'latex',
        includeMetadata: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(result.format).toBe('latex');
      expect(result.output).toContain('\\documentclass{article}');
      expect(result.output).toContain('\\title{Test Spec}');
      expect(result.output).toContain('\\begin{document}');
      expect(result.output).toContain('\\end{document}');
    });

    it('should compile to Markdown format', async () => {
      const options: CompilationOptions = {
        format: 'markdown',
        includeMetadata: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(result.format).toBe('markdown');
      expect(result.output).toContain('# Test Spec');
      expect(result.output).toContain('## Templates');
      expect(result.output).toContain('### Greeting Template');
    });

    it('should compile to HTML format', async () => {
      const options: CompilationOptions = {
        format: 'html',
        includeMetadata: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      expect(result.output).toBeDefined();
      expect(result.format).toBe('html');
      expect(result.output).toContain('<!DOCTYPE html>');
      expect(result.output).toContain('<title>Test Spec</title>');
      expect(result.output).toContain('<h1>Test Spec</h1>');
    });

    it('should handle compilation options correctly', async () => {
      const options: CompilationOptions = {
        format: 'json',
        includeMetadata: false,
        includeTests: false,
        includeValidation: false,
        minify: true,
      };

      const result = await compiler.compile(sampleSpec, options);

      expect(result.success).toBe(true);
      const parsed = JSON.parse(result.output!);
      
      // Should not include metadata
      expect(parsed.templates[0].metadata).toBeUndefined();
      
      // Should be minified (no pretty printing)
      expect(result.output).not.toContain('\n  ');
    });

    it('should handle validation errors', async () => {
      // Create an invalid spec
      const invalidSpec = { ...sampleSpec };
      invalidSpec.templates[0].template = '{{unclosed'; // Invalid template syntax

      const options: CompilationOptions = {
        format: 'json',
        validate: true,
      };

      const result = await compiler.compile(invalidSpec, options);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(e => e.type === 'validation')).toBe(true);
    });
  });

  describe('validate', () => {
    it('should validate a correct spec', async () => {
      const result = await compiler.validate(sampleSpec);

      expect(result.valid).toBe(true);
      expect(result.errors.filter(e => e.severity === 'error')).toHaveLength(0);
    });

    it('should detect template validation issues', async () => {
      // Add a template with unused parameters
      const template = createPromptTemplate(
        'Invalid Template',
        'user',
        'Hello {{name}}!',
        {
          parameters: [
            createParameter('name', 'string', { required: true }),
            createParameter('unused', 'string', { required: false }), // Unused parameter
          ],
        }
      );

      sampleSpec.templates.push(template);

      const result = await compiler.validate(sampleSpec);

      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(w => w.includes('unused'))).toBe(true);
    });

    it('should detect chain validation issues', async () => {
      // Add a chain that references a non-existent template
      const chain = createPromptChain('Invalid Chain');
      chain.steps.push({
        id: 'invalid-step',
        name: 'Invalid Step',
        templateId: 'non-existent-template',
      });

      sampleSpec.chains.push(chain);

      const result = await compiler.validate(sampleSpec);

      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.message.includes('non-existent template'))).toBe(true);
    });

    it('should detect duplicate IDs', async () => {
      // Add a template with duplicate ID
      const duplicateTemplate = { ...sampleSpec.templates[0] };
      sampleSpec.templates.push(duplicateTemplate);

      const result = await compiler.validate(sampleSpec);

      expect(result.valid).toBe(false);
      expect(result.errors.some(e => e.message.includes('Duplicate template ID'))).toBe(true);
    });
  });

  describe('parseFromString', () => {
    it('should parse YAML string to PromptSpec', async () => {
      const yamlOptions: CompilationOptions = { format: 'yaml' };
      const compileResult = await compiler.compile(sampleSpec, yamlOptions);
      
      expect(compileResult.success).toBe(true);
      
      const parsed = await compiler.parseFromString(compileResult.output!, 'yaml');
      
      expect(parsed.specVersion).toBe(sampleSpec.specVersion);
      expect(parsed.metadata.name).toBe(sampleSpec.metadata.name);
      expect(parsed.templates).toHaveLength(sampleSpec.templates.length);
      expect(parsed.chains).toHaveLength(sampleSpec.chains.length);
    });

    it('should parse JSON string to PromptSpec', async () => {
      const jsonOptions: CompilationOptions = { format: 'json' };
      const compileResult = await compiler.compile(sampleSpec, jsonOptions);
      
      expect(compileResult.success).toBe(true);
      
      const parsed = await compiler.parseFromString(compileResult.output!, 'json');
      
      expect(parsed.specVersion).toBe(sampleSpec.specVersion);
      expect(parsed.metadata.name).toBe(sampleSpec.metadata.name);
      expect(parsed.templates).toHaveLength(sampleSpec.templates.length);
    });

    it('should handle invalid format strings', async () => {
      await expect(
        compiler.parseFromString('invalid json', 'json')
      ).rejects.toThrow();
    });

    it('should handle unsupported formats', async () => {
      await expect(
        compiler.parseFromString('content', 'latex' as any)
      ).rejects.toThrow('Parsing from latex format is not supported');
    });
  });

  describe('generateTemplate', () => {
    it('should generate templates for different types', () => {
      const systemTemplate = compiler.generateTemplate('system');
      expect(systemTemplate.type).toBe('system');
      expect(systemTemplate.name).toContain('system');
      expect(systemTemplate.template).toContain('helpful AI assistant');

      const userTemplate = compiler.generateTemplate('user');
      expect(userTemplate.type).toBe('user');
      expect(userTemplate.template).toContain('{{task}}');

      const functionTemplate = compiler.generateTemplate('function');
      expect(functionTemplate.type).toBe('function');
      expect(functionTemplate.template).toContain('{{functionName}}');
    });

    it('should generate templates with unique IDs', () => {
      const template1 = compiler.generateTemplate('user');
      const template2 = compiler.generateTemplate('user');
      
      expect(template1.id).not.toBe(template2.id);
    });

    it('should generate templates with proper metadata', () => {
      const template = compiler.generateTemplate('assistant');
      
      expect(template.metadata?.created).toBeInstanceOf(Date);
      expect(template.metadata?.updated).toBeInstanceOf(Date);
      expect(template.metadata?.tags).toContain('assistant');
      expect(template.metadata?.difficulty).toBe('beginner');
    });
  });
});
