// Main exports
export { CatalystCore } from './catalyst-core.js';
export type { CatalystCoreOptions } from './catalyst-core.js';

// Component exports
export { CatalystIntentAnalyzer } from './intent-analyzer.js';
export { CatalystContextManager } from './context-manager.js';
export { CatalystRoutingEngine } from './routing-engine.js';
export { CatalystOptimizationCoordinator } from './optimization-coordinator.js';
export { CatalystLearningModule } from './learning-module.js';

// Type exports
export type {
  // Core types
  IntentType,
  Intent,
  Context,
  ModelConfig,
  RouteConfig,
  ProcessingResult,
  LearningData,
  
  // Interface types
  IntentAnalyzer,
  ContextManager,
  RoutingEngine,
  OptimizationCoordinator,
  LearningModule,
  
  // Schema types (for validation)
} from './types.js';

// Schema exports for validation
export {
  IntentTypeSchema,
  IntentSchema,
  ContextSchema,
  ModelConfigSchema,
  RouteConfigSchema,
  ProcessingResultSchema,
  LearningDataSchema,
} from './types.js';

// Utility functions
export const createCatalystCore = (options?: import('./catalyst-core.js').CatalystCoreOptions) => {
  return new CatalystCore(options);
};

// Version
export const VERSION = '0.0.0';
