# Catalyst v3 - The Complete AI-Powered Prompt Engineering Ecosystem

A comprehensive, multi-model prompt engineering ecosystem built with modern web technologies and AI-first architecture.

## 🚀 Features

- **Catalyst AI Core**: Intent analysis, context management, and intelligent routing
- **PromptSpec**: Specification compiler for YAML/JSON/LaTeX output
- **Prompt4Me**: Guided optimizer with tiered capability matrix
- **PromptTurbines**: 4-stage iterative enhancer with simulation testing
- **PromptForge**: Template library with auto-expansion and meta-prompting
- **PromptSynth**: Dynamic prompt-chaining workflow engine
- **PromptAlchemy**: Tone/style transformer and prompt fusion
- **PromptPortals**: Model-specific adapters with smart routing
- **OpenRouter Adapter**: Single-key gateway to multiple AI models

## 🏗️ Architecture

### Apps
- **Web**: Next.js 14 PWA with Tailwind CSS and shadcn/ui
- **Gateway**: Fastify v4 + Mercurius GraphQL API

### Packages
- `catalyst-core`: Core AI system
- `promptspec`: Specification compiler
- `prompt4me`: Guided optimizer
- `turbines`: Iterative enhancer
- `forge`: Template system
- `synth`: Workflow engine
- `alchemy`: Style transformer
- `portals`: Model adapters
- `openrouter-adapter`: Multi-model gateway

## 🛠️ Development

```bash
# Install dependencies
npm install

# Start development servers
npm run dev

# Build all packages
npm run build

# Run tests
npm run test

# Run E2E tests
npm run test:e2e

# Lint code
npm run lint

# Format code
npm run format
```

## 📦 Deployment

```bash
# Build production images
docker-compose build

# Start production stack
docker-compose up -d
```

## 🧪 Testing

- Unit tests with Jest (85%+ coverage requirement)
- E2E tests with Playwright
- API contract tests
- Lighthouse PWA scoring (≥90 required)

## 🔧 Tech Stack

- **Frontend**: Next.js 14, React Server Components, Tailwind CSS, shadcn/ui
- **Backend**: Fastify, Mercurius GraphQL, TypeScript
- **Database**: PostgreSQL, Redis, Vector Database
- **Infrastructure**: Docker, GitHub Actions, Terraform (optional)
- **Monitoring**: OpenTelemetry, Sentry

## 📄 License

MIT License - see LICENSE file for details.
