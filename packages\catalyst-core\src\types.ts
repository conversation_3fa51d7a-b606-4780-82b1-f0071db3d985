import { z } from 'zod';

// Core Intent Types
export const IntentTypeSchema = z.enum([
  'optimize',
  'generate',
  'analyze',
  'transform',
  'chain',
  'route',
  'synthesize',
]);

export type IntentType = z.infer<typeof IntentTypeSchema>;

// Context Types
export const ContextSchema = z.object({
  id: z.string(),
  userId: z.string().optional(),
  sessionId: z.string(),
  timestamp: z.date(),
  metadata: z.record(z.unknown()).optional(),
  history: z.array(z.unknown()).optional(),
});

export type Context = z.infer<typeof ContextSchema>;

// Intent Schema
export const IntentSchema = z.object({
  id: z.string(),
  type: IntentTypeSchema,
  prompt: z.string(),
  context: ContextSchema,
  priority: z.number().min(0).max(10).default(5),
  requirements: z.record(z.unknown()).optional(),
  constraints: z.record(z.unknown()).optional(),
});

export type Intent = z.infer<typeof IntentSchema>;

// Model Configuration
export const ModelConfigSchema = z.object({
  provider: z.string(),
  model: z.string(),
  maxTokens: z.number().optional(),
  temperature: z.number().min(0).max(2).optional(),
  topP: z.number().min(0).max(1).optional(),
  frequencyPenalty: z.number().min(-2).max(2).optional(),
  presencePenalty: z.number().min(-2).max(2).optional(),
});

export type ModelConfig = z.infer<typeof ModelConfigSchema>;

// Route Configuration
export const RouteConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  intentTypes: z.array(IntentTypeSchema),
  modelConfig: ModelConfigSchema,
  priority: z.number().min(0).max(10).default(5),
  conditions: z.record(z.unknown()).optional(),
  fallback: z.string().optional(),
});

export type RouteConfig = z.infer<typeof RouteConfigSchema>;

// Processing Result
export const ProcessingResultSchema = z.object({
  id: z.string(),
  intentId: z.string(),
  routeId: z.string(),
  result: z.unknown(),
  metadata: z.record(z.unknown()).optional(),
  performance: z.object({
    startTime: z.date(),
    endTime: z.date(),
    duration: z.number(),
    tokensUsed: z.number().optional(),
  }),
  success: z.boolean(),
  error: z.string().optional(),
});

export type ProcessingResult = z.infer<typeof ProcessingResultSchema>;

// Learning Data
export const LearningDataSchema = z.object({
  intentId: z.string(),
  routeId: z.string(),
  performance: z.number().min(0).max(1),
  feedback: z.string().optional(),
  timestamp: z.date(),
  metadata: z.record(z.unknown()).optional(),
});

export type LearningData = z.infer<typeof LearningDataSchema>;

// Core Interfaces
export interface IntentAnalyzer {
  analyze(prompt: string, context: Context): Promise<Intent>;
  extractRequirements(prompt: string): Promise<Record<string, unknown>>;
  classifyIntent(prompt: string): Promise<IntentType>;
}

export interface ContextManager {
  createContext(sessionId: string, userId?: string): Context;
  updateContext(contextId: string, updates: Partial<Context>): Promise<Context>;
  getContext(contextId: string): Promise<Context | null>;
  addToHistory(contextId: string, entry: unknown): Promise<void>;
  clearHistory(contextId: string): Promise<void>;
}

export interface RoutingEngine {
  addRoute(config: RouteConfig): void;
  removeRoute(routeId: string): void;
  findBestRoute(intent: Intent): Promise<RouteConfig | null>;
  getAllRoutes(): RouteConfig[];
  updateRoute(routeId: string, updates: Partial<RouteConfig>): void;
}

export interface OptimizationCoordinator {
  optimize(intent: Intent, route: RouteConfig): Promise<Intent>;
  suggestImprovements(result: ProcessingResult): Promise<string[]>;
  analyzePerformance(results: ProcessingResult[]): Promise<Record<string, number>>;
}

export interface LearningModule {
  learn(data: LearningData): Promise<void>;
  getInsights(intentType: IntentType): Promise<Record<string, unknown>>;
  updateRouteWeights(routeId: string, performance: number): Promise<void>;
  exportLearningData(): Promise<LearningData[]>;
}
