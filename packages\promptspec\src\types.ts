import { z } from 'zod';

// Core PromptSpec Types
export const PromptSpecVersionSchema = z.enum(['1.0', '1.1', '2.0']);
export type PromptSpecVersion = z.infer<typeof PromptSpecVersionSchema>;

export const PromptTypeSchema = z.enum([
  'system',
  'user',
  'assistant',
  'function',
  'tool',
  'template',
  'chain',
  'workflow'
]);
export type PromptType = z.infer<typeof PromptTypeSchema>;

export const OutputFormatSchema = z.enum(['yaml', 'json', 'latex', 'markdown', 'html']);
export type OutputFormat = z.infer<typeof OutputFormatSchema>;

// Parameter Definition
export const ParameterSchema = z.object({
  name: z.string(),
  type: z.enum(['string', 'number', 'boolean', 'array', 'object']),
  description: z.string().optional(),
  required: z.boolean().default(false),
  default: z.unknown().optional(),
  constraints: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
    enum: z.array(z.unknown()).optional(),
  }).optional(),
  examples: z.array(z.unknown()).optional(),
});
export type Parameter = z.infer<typeof ParameterSchema>;

// Model Configuration
export const ModelConfigSchema = z.object({
  provider: z.string(),
  model: z.string(),
  version: z.string().optional(),
  parameters: z.object({
    temperature: z.number().min(0).max(2).optional(),
    maxTokens: z.number().positive().optional(),
    topP: z.number().min(0).max(1).optional(),
    frequencyPenalty: z.number().min(-2).max(2).optional(),
    presencePenalty: z.number().min(-2).max(2).optional(),
    stopSequences: z.array(z.string()).optional(),
  }).optional(),
});
export type ModelConfig = z.infer<typeof ModelConfigSchema>;

// Validation Rule
export const ValidationRuleSchema = z.object({
  name: z.string(),
  type: z.enum(['length', 'format', 'content', 'custom']),
  condition: z.string(),
  message: z.string(),
  severity: z.enum(['error', 'warning', 'info']).default('error'),
});
export type ValidationRule = z.infer<typeof ValidationRuleSchema>;

// Test Case
export const TestCaseSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  input: z.record(z.unknown()),
  expectedOutput: z.unknown().optional(),
  expectedPattern: z.string().optional(),
  tags: z.array(z.string()).optional(),
  timeout: z.number().positive().optional(),
});
export type TestCase = z.infer<typeof TestCaseSchema>;

// Prompt Template
export const PromptTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  type: PromptTypeSchema,
  template: z.string(),
  parameters: z.array(ParameterSchema).default([]),
  modelConfig: ModelConfigSchema.optional(),
  validationRules: z.array(ValidationRuleSchema).default([]),
  testCases: z.array(TestCaseSchema).default([]),
  metadata: z.object({
    author: z.string().optional(),
    created: z.date().optional(),
    updated: z.date().optional(),
    tags: z.array(z.string()).optional(),
    category: z.string().optional(),
    difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
    estimatedTokens: z.number().positive().optional(),
  }).optional(),
});
export type PromptTemplate = z.infer<typeof PromptTemplateSchema>;

// Prompt Chain Step
export const ChainStepSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  templateId: z.string(),
  inputMapping: z.record(z.string()).optional(),
  outputMapping: z.record(z.string()).optional(),
  condition: z.string().optional(),
  retryPolicy: z.object({
    maxRetries: z.number().min(0).default(0),
    backoffMs: z.number().positive().default(1000),
  }).optional(),
});
export type ChainStep = z.infer<typeof ChainStepSchema>;

// Prompt Chain
export const PromptChainSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  version: z.string().default('1.0.0'),
  steps: z.array(ChainStepSchema),
  globalParameters: z.array(ParameterSchema).default([]),
  errorHandling: z.object({
    strategy: z.enum(['fail-fast', 'continue', 'retry']).default('fail-fast'),
    fallbackTemplate: z.string().optional(),
  }).optional(),
  metadata: z.object({
    author: z.string().optional(),
    created: z.date().optional(),
    updated: z.date().optional(),
    tags: z.array(z.string()).optional(),
    category: z.string().optional(),
  }).optional(),
});
export type PromptChain = z.infer<typeof PromptChainSchema>;

// Complete PromptSpec Document
export const PromptSpecSchema = z.object({
  specVersion: PromptSpecVersionSchema.default('2.0'),
  metadata: z.object({
    name: z.string(),
    description: z.string().optional(),
    version: z.string().default('1.0.0'),
    author: z.string().optional(),
    license: z.string().optional(),
    created: z.date().optional(),
    updated: z.date().optional(),
    tags: z.array(z.string()).optional(),
  }),
  templates: z.array(PromptTemplateSchema).default([]),
  chains: z.array(PromptChainSchema).default([]),
  globalConfig: z.object({
    defaultModel: ModelConfigSchema.optional(),
    globalParameters: z.array(ParameterSchema).default([]),
    validationRules: z.array(ValidationRuleSchema).default([]),
  }).optional(),
});
export type PromptSpec = z.infer<typeof PromptSpecSchema>;

// Compilation Options
export const CompilationOptionsSchema = z.object({
  format: OutputFormatSchema,
  includeMetadata: z.boolean().default(true),
  includeTests: z.boolean().default(true),
  includeValidation: z.boolean().default(true),
  minify: z.boolean().default(false),
  validate: z.boolean().default(true),
  outputPath: z.string().optional(),
  templateEngine: z.enum(['handlebars', 'mustache', 'ejs']).default('handlebars'),
});
export type CompilationOptions = z.infer<typeof CompilationOptionsSchema>;

// Compilation Result
export const CompilationResultSchema = z.object({
  success: z.boolean(),
  output: z.string().optional(),
  format: OutputFormatSchema,
  errors: z.array(z.object({
    type: z.enum(['validation', 'compilation', 'template']),
    message: z.string(),
    location: z.string().optional(),
    severity: z.enum(['error', 'warning', 'info']),
  })).default([]),
  warnings: z.array(z.string()).default([]),
  metadata: z.object({
    compiledAt: z.date(),
    specVersion: z.string(),
    outputSize: z.number(),
    templateCount: z.number(),
    chainCount: z.number(),
  }),
});
export type CompilationResult = z.infer<typeof CompilationResultSchema>;

// Interfaces
export interface PromptSpecCompiler {
  compile(spec: PromptSpec, options: CompilationOptions): Promise<CompilationResult>;
  validate(spec: PromptSpec): Promise<ValidationResult>;
  parseFromString(content: string, format: OutputFormat): Promise<PromptSpec>;
  generateTemplate(templateType: PromptType): PromptTemplate;
}

export interface TemplateEngine {
  render(template: string, context: Record<string, unknown>): Promise<string>;
  validate(template: string): Promise<boolean>;
  extractVariables(template: string): string[];
}

export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    path: string;
    message: string;
    severity: 'error' | 'warning' | 'info';
  }>;
  warnings: string[];
}

// Export utility types
export type PromptSpecElement = PromptTemplate | PromptChain;
export type CompilerError = z.infer<typeof CompilationResultSchema>['errors'][0];
export type SpecMetadata = z.infer<typeof PromptSpecSchema>['metadata'];
