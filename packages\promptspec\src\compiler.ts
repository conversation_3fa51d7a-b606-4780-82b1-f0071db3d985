import { stringify as yamlStringify, parse as yamlParse } from 'yaml';
import { v4 as uuidv4 } from 'uuid';
import type {
  PromptSpecCompiler,
  PromptSpec,
  CompilationOptions,
  CompilationResult,
  ValidationResult,
  OutputFormat,
  PromptType,
  PromptTemplate,
  CompilerError,
} from './types.js';
import {
  PromptSpecSchema,
  CompilationOptionsSchema,
  PromptTemplateSchema,
} from './types.js';

export class CatalystPromptSpecCompiler implements PromptSpecCompiler {
  private templateEngines: Map<string, any> = new Map();

  constructor() {
    this.initializeTemplateEngines();
  }

  async compile(spec: PromptSpec, options: CompilationOptions): Promise<CompilationResult> {
    const startTime = new Date();
    const errors: CompilerError[] = [];
    const warnings: string[] = [];

    try {
      // Validate options
      const validatedOptions = CompilationOptionsSchema.parse(options);

      // Validate spec if requested
      if (validatedOptions.validate) {
        const validationResult = await this.validate(spec);
        if (!validationResult.valid) {
          errors.push(...validationResult.errors.map(err => ({
            type: 'validation' as const,
            message: err.message,
            location: err.path,
            severity: err.severity,
          })));
        }
        warnings.push(...validationResult.warnings);
      }

      // If there are validation errors and we're in strict mode, return early
      if (errors.some(e => e.severity === 'error') && validatedOptions.validate) {
        return {
          success: false,
          format: validatedOptions.format,
          errors,
          warnings,
          metadata: {
            compiledAt: new Date(),
            specVersion: spec.specVersion,
            outputSize: 0,
            templateCount: spec.templates.length,
            chainCount: spec.chains.length,
          },
        };
      }

      // Compile based on format
      let output: string;
      switch (validatedOptions.format) {
        case 'yaml':
          output = await this.compileToYAML(spec, validatedOptions);
          break;
        case 'json':
          output = await this.compileToJSON(spec, validatedOptions);
          break;
        case 'latex':
          output = await this.compileToLaTeX(spec, validatedOptions);
          break;
        case 'markdown':
          output = await this.compileToMarkdown(spec, validatedOptions);
          break;
        case 'html':
          output = await this.compileToHTML(spec, validatedOptions);
          break;
        default:
          throw new Error(`Unsupported output format: ${validatedOptions.format}`);
      }

      return {
        success: true,
        output,
        format: validatedOptions.format,
        errors,
        warnings,
        metadata: {
          compiledAt: new Date(),
          specVersion: spec.specVersion,
          outputSize: output.length,
          templateCount: spec.templates.length,
          chainCount: spec.chains.length,
        },
      };

    } catch (error) {
      errors.push({
        type: 'compilation',
        message: error instanceof Error ? error.message : 'Unknown compilation error',
        severity: 'error',
      });

      return {
        success: false,
        format: options.format,
        errors,
        warnings,
        metadata: {
          compiledAt: new Date(),
          specVersion: spec.specVersion || '2.0',
          outputSize: 0,
          templateCount: spec.templates?.length || 0,
          chainCount: spec.chains?.length || 0,
        },
      };
    }
  }

  async validate(spec: PromptSpec): Promise<ValidationResult> {
    const errors: ValidationResult['errors'] = [];
    const warnings: string[] = [];

    try {
      // Schema validation
      PromptSpecSchema.parse(spec);
    } catch (error) {
      if (error instanceof Error) {
        errors.push({
          path: 'root',
          message: error.message,
          severity: 'error',
        });
      }
    }

    // Custom validation rules
    await this.validateTemplates(spec, errors, warnings);
    await this.validateChains(spec, errors, warnings);
    await this.validateReferences(spec, errors, warnings);

    return {
      valid: errors.filter(e => e.severity === 'error').length === 0,
      errors,
      warnings,
    };
  }

  async parseFromString(content: string, format: OutputFormat): Promise<PromptSpec> {
    try {
      let parsed: any;

      switch (format) {
        case 'yaml':
          parsed = yamlParse(content);
          break;
        case 'json':
          parsed = JSON.parse(content);
          break;
        default:
          throw new Error(`Parsing from ${format} format is not supported`);
      }

      // Convert date strings back to Date objects
      this.convertDates(parsed);

      return PromptSpecSchema.parse(parsed);
    } catch (error) {
      throw new Error(`Failed to parse PromptSpec from ${format}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  generateTemplate(templateType: PromptType): PromptTemplate {
    const baseTemplate: PromptTemplate = {
      id: uuidv4(),
      name: `New ${templateType} Template`,
      description: `A new ${templateType} prompt template`,
      version: '1.0.0',
      type: templateType,
      template: this.getDefaultTemplateContent(templateType),
      parameters: [],
      validationRules: [],
      testCases: [],
      metadata: {
        created: new Date(),
        updated: new Date(),
        tags: [templateType],
        difficulty: 'beginner',
      },
    };

    return baseTemplate;
  }

  // Private compilation methods
  private async compileToYAML(spec: PromptSpec, options: CompilationOptions): Promise<string> {
    const cleanSpec = this.prepareSpecForOutput(spec, options);
    return yamlStringify(cleanSpec, {
      indent: 2,
      lineWidth: 120,
      minContentWidth: 20,
    });
  }

  private async compileToJSON(spec: PromptSpec, options: CompilationOptions): Promise<string> {
    const cleanSpec = this.prepareSpecForOutput(spec, options);
    return JSON.stringify(cleanSpec, null, options.minify ? 0 : 2);
  }

  private async compileToLaTeX(spec: PromptSpec, options: CompilationOptions): Promise<string> {
    const latex = [
      '\\documentclass{article}',
      '\\usepackage[utf8]{inputenc}',
      '\\usepackage{listings}',
      '\\usepackage{xcolor}',
      '\\usepackage{geometry}',
      '\\geometry{margin=1in}',
      '',
      '\\title{' + this.escapeLatex(spec.metadata.name) + '}',
      '\\author{' + this.escapeLatex(spec.metadata.author || 'Unknown') + '}',
      '\\date{\\today}',
      '',
      '\\begin{document}',
      '\\maketitle',
      '',
    ];

    if (spec.metadata.description) {
      latex.push('\\section{Description}');
      latex.push(this.escapeLatex(spec.metadata.description));
      latex.push('');
    }

    // Templates section
    if (spec.templates.length > 0) {
      latex.push('\\section{Templates}');
      for (const template of spec.templates) {
        latex.push(`\\subsection{${this.escapeLatex(template.name)}}`);
        if (template.description) {
          latex.push(this.escapeLatex(template.description));
        }
        latex.push('\\begin{lstlisting}');
        latex.push(template.template);
        latex.push('\\end{lstlisting}');
        latex.push('');
      }
    }

    // Chains section
    if (spec.chains.length > 0) {
      latex.push('\\section{Chains}');
      for (const chain of spec.chains) {
        latex.push(`\\subsection{${this.escapeLatex(chain.name)}}`);
        if (chain.description) {
          latex.push(this.escapeLatex(chain.description));
        }
        latex.push('');
      }
    }

    latex.push('\\end{document}');
    return latex.join('\n');
  }

  private async compileToMarkdown(spec: PromptSpec, options: CompilationOptions): Promise<string> {
    const md = [];

    // Title and metadata
    md.push(`# ${spec.metadata.name}`);
    md.push('');
    if (spec.metadata.description) {
      md.push(spec.metadata.description);
      md.push('');
    }

    // Metadata table
    md.push('## Metadata');
    md.push('| Property | Value |');
    md.push('|----------|-------|');
    md.push(`| Version | ${spec.metadata.version} |`);
    md.push(`| Spec Version | ${spec.specVersion} |`);
    if (spec.metadata.author) md.push(`| Author | ${spec.metadata.author} |`);
    if (spec.metadata.license) md.push(`| License | ${spec.metadata.license} |`);
    md.push('');

    // Templates
    if (spec.templates.length > 0) {
      md.push('## Templates');
      for (const template of spec.templates) {
        md.push(`### ${template.name}`);
        if (template.description) {
          md.push(template.description);
        }
        md.push('');
        md.push('```');
        md.push(template.template);
        md.push('```');
        md.push('');

        if (template.parameters.length > 0) {
          md.push('#### Parameters');
          md.push('| Name | Type | Required | Description |');
          md.push('|------|------|----------|-------------|');
          for (const param of template.parameters) {
            md.push(`| ${param.name} | ${param.type} | ${param.required ? 'Yes' : 'No'} | ${param.description || ''} |`);
          }
          md.push('');
        }
      }
    }

    // Chains
    if (spec.chains.length > 0) {
      md.push('## Chains');
      for (const chain of spec.chains) {
        md.push(`### ${chain.name}`);
        if (chain.description) {
          md.push(chain.description);
        }
        md.push('');

        md.push('#### Steps');
        for (let i = 0; i < chain.steps.length; i++) {
          const step = chain.steps[i];
          md.push(`${i + 1}. **${step.name}** (Template: ${step.templateId})`);
          if (step.description) {
            md.push(`   ${step.description}`);
          }
        }
        md.push('');
      }
    }

    return md.join('\n');
  }

  private async compileToHTML(spec: PromptSpec, options: CompilationOptions): Promise<string> {
    const html = [
      '<!DOCTYPE html>',
      '<html lang="en">',
      '<head>',
      '    <meta charset="UTF-8">',
      '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
      `    <title>${this.escapeHtml(spec.metadata.name)}</title>`,
      '    <style>',
      '        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }',
      '        .template { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }',
      '        .chain { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; background: #f9f9f9; }',
      '        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }',
      '        table { border-collapse: collapse; width: 100%; }',
      '        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }',
      '        th { background-color: #f2f2f2; }',
      '    </style>',
      '</head>',
      '<body>',
      `    <h1>${this.escapeHtml(spec.metadata.name)}</h1>`,
    ];

    if (spec.metadata.description) {
      html.push(`    <p>${this.escapeHtml(spec.metadata.description)}</p>`);
    }

    // Templates
    if (spec.templates.length > 0) {
      html.push('    <h2>Templates</h2>');
      for (const template of spec.templates) {
        html.push('    <div class="template">');
        html.push(`        <h3>${this.escapeHtml(template.name)}</h3>`);
        if (template.description) {
          html.push(`        <p>${this.escapeHtml(template.description)}</p>`);
        }
        html.push('        <pre><code>' + this.escapeHtml(template.template) + '</code></pre>');
        html.push('    </div>');
      }
    }

    // Chains
    if (spec.chains.length > 0) {
      html.push('    <h2>Chains</h2>');
      for (const chain of spec.chains) {
        html.push('    <div class="chain">');
        html.push(`        <h3>${this.escapeHtml(chain.name)}</h3>`);
        if (chain.description) {
          html.push(`        <p>${this.escapeHtml(chain.description)}</p>`);
        }
        html.push('        <h4>Steps</h4>');
        html.push('        <ol>');
        for (const step of chain.steps) {
          html.push(`            <li><strong>${this.escapeHtml(step.name)}</strong> (Template: ${this.escapeHtml(step.templateId)})`);
          if (step.description) {
            html.push(`                <br>${this.escapeHtml(step.description)}`);
          }
          html.push('            </li>');
        }
        html.push('        </ol>');
        html.push('    </div>');
      }
    }

    html.push('</body>');
    html.push('</html>');

    return html.join('\n');
  }

  // Utility methods
  private prepareSpecForOutput(spec: PromptSpec, options: CompilationOptions): any {
    const output = { ...spec };

    if (!options.includeMetadata) {
      delete output.metadata;
      output.templates = output.templates.map(t => {
        const { metadata, ...rest } = t;
        return rest;
      });
      output.chains = output.chains.map(c => {
        const { metadata, ...rest } = c;
        return rest;
      });
    }

    if (!options.includeTests) {
      output.templates = output.templates.map(t => {
        const { testCases, ...rest } = t;
        return rest;
      });
    }

    if (!options.includeValidation) {
      output.templates = output.templates.map(t => {
        const { validationRules, ...rest } = t;
        return rest;
      });
      if (output.globalConfig) {
        const { validationRules, ...rest } = output.globalConfig;
        output.globalConfig = rest;
      }
    }

    return output;
  }

  private convertDates(obj: any): void {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (typeof obj[key] === 'string' && this.isDateString(obj[key])) {
          obj[key] = new Date(obj[key]);
        } else if (typeof obj[key] === 'object') {
          this.convertDates(obj[key]);
        }
      }
    }
  }

  private isDateString(str: string): boolean {
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(str);
  }

  private getDefaultTemplateContent(type: PromptType): string {
    const templates = {
      system: 'You are a helpful AI assistant. Please provide accurate and helpful responses.',
      user: 'Please help me with {{task}}. Here are the details: {{details}}',
      assistant: 'I understand you need help with {{task}}. Let me assist you with that.',
      function: 'Execute the following function: {{functionName}} with parameters: {{parameters}}',
      tool: 'Use the {{toolName}} tool to {{action}} with the following input: {{input}}',
      template: 'This is a template for {{purpose}}. Please customize as needed: {{content}}',
      chain: 'Step {{stepNumber}}: {{instruction}}',
      workflow: 'Workflow: {{workflowName}}\nStep {{currentStep}} of {{totalSteps}}: {{stepDescription}}',
    };
    return templates[type] || 'Please provide your prompt template here.';
  }

  private async validateTemplates(spec: PromptSpec, errors: ValidationResult['errors'], warnings: string[]): Promise<void> {
    for (const template of spec.templates) {
      // Check for duplicate IDs
      const duplicates = spec.templates.filter(t => t.id === template.id);
      if (duplicates.length > 1) {
        errors.push({
          path: `templates[${template.id}]`,
          message: `Duplicate template ID: ${template.id}`,
          severity: 'error',
        });
      }

      // Validate template syntax
      try {
        this.extractVariables(template.template);
      } catch (error) {
        errors.push({
          path: `templates[${template.id}].template`,
          message: `Invalid template syntax: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error',
        });
      }

      // Check parameter usage
      const templateVars = this.extractVariables(template.template);
      const paramNames = template.parameters.map(p => p.name);

      for (const variable of templateVars) {
        if (!paramNames.includes(variable)) {
          warnings.push(`Template ${template.id} uses variable '${variable}' but no parameter is defined`);
        }
      }

      for (const param of template.parameters) {
        if (!templateVars.includes(param.name)) {
          warnings.push(`Template ${template.id} defines parameter '${param.name}' but it's not used in the template`);
        }
      }
    }
  }

  private async validateChains(spec: PromptSpec, errors: ValidationResult['errors'], warnings: string[]): Promise<void> {
    const templateIds = new Set(spec.templates.map(t => t.id));

    for (const chain of spec.chains) {
      // Check for duplicate IDs
      const duplicates = spec.chains.filter(c => c.id === chain.id);
      if (duplicates.length > 1) {
        errors.push({
          path: `chains[${chain.id}]`,
          message: `Duplicate chain ID: ${chain.id}`,
          severity: 'error',
        });
      }

      // Validate step references
      for (const step of chain.steps) {
        if (!templateIds.has(step.templateId)) {
          errors.push({
            path: `chains[${chain.id}].steps[${step.id}]`,
            message: `Step references non-existent template: ${step.templateId}`,
            severity: 'error',
          });
        }
      }

      // Check for circular dependencies
      if (this.hasCircularDependency(chain, spec)) {
        errors.push({
          path: `chains[${chain.id}]`,
          message: 'Chain contains circular dependency',
          severity: 'error',
        });
      }
    }
  }

  private async validateReferences(spec: PromptSpec, errors: ValidationResult['errors'], warnings: string[]): Promise<void> {
    // Validate global config references
    if (spec.globalConfig?.defaultModel) {
      // Could validate model availability here
    }

    // Validate fallback template references
    const templateIds = new Set(spec.templates.map(t => t.id));

    for (const chain of spec.chains) {
      if (chain.errorHandling?.fallbackTemplate && !templateIds.has(chain.errorHandling.fallbackTemplate)) {
        errors.push({
          path: `chains[${chain.id}].errorHandling.fallbackTemplate`,
          message: `References non-existent fallback template: ${chain.errorHandling.fallbackTemplate}`,
          severity: 'error',
        });
      }
    }
  }

  private hasCircularDependency(chain: any, spec: PromptSpec): boolean {
    // Simplified circular dependency check
    // In a real implementation, this would be more sophisticated
    const visited = new Set();
    const recursionStack = new Set();

    const visit = (stepId: string): boolean => {
      if (recursionStack.has(stepId)) return true;
      if (visited.has(stepId)) return false;

      visited.add(stepId);
      recursionStack.add(stepId);

      // Check dependencies (simplified)
      recursionStack.delete(stepId);
      return false;
    };

    for (const step of chain.steps) {
      if (visit(step.id)) return true;
    }

    return false;
  }

  private extractVariables(template: string): string[] {
    const variables: string[] = [];
    const regex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = regex.exec(template)) !== null) {
      const variable = match[1].trim();
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  private escapeLatex(text: string): string {
    return text
      .replace(/\\/g, '\\textbackslash{}')
      .replace(/[{}]/g, '\\$&')
      .replace(/[#$%&_]/g, '\\$&')
      .replace(/\^/g, '\\textasciicircum{}')
      .replace(/~/g, '\\textasciitilde{}');
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  private initializeTemplateEngines(): void {
    // Initialize template engines (simplified)
    // In a real implementation, you'd integrate with actual template engines
    this.templateEngines.set('handlebars', {
      render: async (template: string, context: Record<string, unknown>) => {
        // Simple variable replacement for demo
        let result = template;
        for (const [key, value] of Object.entries(context)) {
          result = result.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), String(value));
        }
        return result;
      },
    });
  }
}
