# Database
DATABASE_URL="postgresql://catalyst:password@localhost:5432/catalyst"
REDIS_URL="redis://localhost:6379"

# Vector Database
VECTOR_DB_URL="postgresql://catalyst:password@localhost:5432/catalyst_vector"

# OpenRouter API
OPENROUTER_API_KEY="your_openrouter_api_key_here"

# Authentication
JWT_SECRET="your_jwt_secret_here"
AUTH_SECRET="your_auth_secret_here"

# API Configuration
API_PORT=4000
WEB_PORT=3000

# Monitoring
SENTRY_DSN="your_sentry_dsn_here"
OTEL_EXPORTER_OTLP_ENDPOINT="http://localhost:4317"

# Development
NODE_ENV="development"
LOG_LEVEL="debug"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# AI Model Configuration
DEFAULT_MODEL="gpt-4o"
FALLBACK_MODEL="claude-3-5-sonnet"
MAX_TOKENS=4096
TEMPERATURE=0.7
