import { describe, it, expect, beforeEach } from '@jest/globals';
import { CatalystCore } from './catalyst-core.js';
import type { RouteConfig } from './types.js';

describe('CatalystCore', () => {
  let catalyst: CatalystCore;

  beforeEach(() => {
    catalyst = new CatalystCore({
      enableLearning: true,
      enableOptimization: true,
    });
  });

  describe('process', () => {
    it('should process a simple generation request', async () => {
      const result = await catalyst.process(
        'Generate a hello world example in Python',
        'test-session-1',
        'test-user-1'
      );

      expect(result.success).toBe(true);
      expect(result.intentId).toBeDefined();
      expect(result.routeId).toBeDefined();
      expect(result.result).toBeDefined();
      expect(result.performance.duration).toBeGreaterThan(0);
    });

    it('should process an analysis request', async () => {
      const result = await catalyst.process(
        'Analyze the performance of this code snippet',
        'test-session-2',
        'test-user-1'
      );

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('type', 'analysis');
    });

    it('should process an optimization request', async () => {
      const result = await catalyst.process(
        'Optimize this prompt for better results',
        'test-session-3',
        'test-user-1'
      );

      expect(result.success).toBe(true);
      expect(result.result).toHaveProperty('type', 'optimization');
    });

    it('should handle errors gracefully', async () => {
      // This would require mocking the internal components to throw errors
      // For now, we'll test the basic structure
      const result = await catalyst.process(
        'Test prompt',
        'test-session-error'
      );

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('performance');
    });
  });

  describe('route management', () => {
    it('should add and remove routes', () => {
      const testRoute: RouteConfig = {
        id: 'test-route',
        name: 'Test Route',
        description: 'A test route for unit testing',
        intentTypes: ['generate'],
        modelConfig: {
          provider: 'test',
          model: 'test-model',
          maxTokens: 1000,
          temperature: 0.7,
        },
        priority: 5,
      };

      catalyst.addRoute(testRoute);
      
      // Verify route was added (we'd need to expose a method to check this)
      expect(() => catalyst.addRoute(testRoute)).not.toThrow();
      
      catalyst.removeRoute('test-route');
      
      // Verify route was removed
      expect(() => catalyst.removeRoute('test-route')).not.toThrow();
    });
  });

  describe('insights and analytics', () => {
    it('should provide insights for intent types', async () => {
      // First, process some requests to generate data
      await catalyst.process('Generate code', 'session-1');
      await catalyst.process('Create documentation', 'session-2');
      
      const insights = await catalyst.getInsights('generate');
      
      expect(insights).toBeDefined();
      expect(typeof insights).toBe('object');
    });

    it('should provide system statistics', async () => {
      const stats = await catalyst.getStats();
      
      expect(stats).toHaveProperty('contexts');
      expect(stats).toHaveProperty('learning');
      expect(stats).toHaveProperty('routes');
      expect(stats).toHaveProperty('system');
      
      expect(stats.system.learningEnabled).toBe(true);
      expect(stats.system.optimizationEnabled).toBe(true);
    });
  });

  describe('learning data management', () => {
    it('should export and import learning data', async () => {
      // Process some requests to generate learning data
      await catalyst.process('Test prompt 1', 'session-1');
      await catalyst.process('Test prompt 2', 'session-2');
      
      const exportedData = await catalyst.exportLearningData();
      expect(Array.isArray(exportedData)).toBe(true);
      
      // Clear data
      await catalyst.clearLearningData();
      
      // Import data back
      await catalyst.importLearningData(exportedData);
      
      const reimportedData = await catalyst.exportLearningData();
      expect(reimportedData.length).toBeGreaterThanOrEqual(0);
    });

    it('should clear learning data', async () => {
      // Process a request to generate learning data
      await catalyst.process('Test prompt', 'session-1');
      
      await catalyst.clearLearningData();
      
      const data = await catalyst.exportLearningData();
      expect(data.length).toBe(0);
    });
  });

  describe('performance analysis', () => {
    it('should analyze performance of results', async () => {
      const result1 = await catalyst.process('Test prompt 1', 'session-1');
      const result2 = await catalyst.process('Test prompt 2', 'session-2');
      
      const analysis = await catalyst.analyzePerformance([result1, result2]);
      
      expect(analysis).toBeDefined();
      expect(typeof analysis).toBe('object');
      expect(analysis).toHaveProperty('averagePerformance');
      expect(analysis).toHaveProperty('successRate');
    });

    it('should provide suggestions for improvement', async () => {
      const result = await catalyst.process('Test prompt', 'session-1');
      
      const suggestions = await catalyst.getSuggestions(result);
      
      expect(Array.isArray(suggestions)).toBe(true);
    });
  });

  describe('configuration', () => {
    it('should work with learning disabled', async () => {
      const catalystNoLearning = new CatalystCore({
        enableLearning: false,
        enableOptimization: true,
      });

      const result = await catalystNoLearning.process(
        'Test prompt',
        'session-1'
      );

      expect(result.success).toBe(true);
    });

    it('should work with optimization disabled', async () => {
      const catalystNoOptimization = new CatalystCore({
        enableLearning: true,
        enableOptimization: false,
      });

      const result = await catalystNoOptimization.process(
        'Test prompt',
        'session-1'
      );

      expect(result.success).toBe(true);
    });

    it('should work with both learning and optimization disabled', async () => {
      const catalystMinimal = new CatalystCore({
        enableLearning: false,
        enableOptimization: false,
      });

      const result = await catalystMinimal.process(
        'Test prompt',
        'session-1'
      );

      expect(result.success).toBe(true);
    });
  });
});
