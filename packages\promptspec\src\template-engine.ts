import type { TemplateEngine } from './types.js';

export class HandlebarsTemplateEngine implements TemplateEngine {
  async render(template: string, context: Record<string, unknown>): Promise<string> {
    let result = template;

    // Handle simple variable substitution {{variable}}
    const simpleVarRegex = /\{\{([^#/][^}]*)\}\}/g;
    result = result.replace(simpleVarRegex, (match, variable) => {
      const trimmedVar = variable.trim();
      const value = this.getNestedValue(context, trimmedVar);
      return value !== undefined ? String(value) : match;
    });

    // Handle conditional blocks {{#if condition}}...{{/if}}
    const ifRegex = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
    result = result.replace(ifRegex, (match, condition, content) => {
      const conditionValue = this.evaluateCondition(condition.trim(), context);
      return conditionValue ? content : '';
    });

    // Handle loops {{#each array}}...{{/each}}
    const eachRegex = /\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
    result = result.replace(eachRegex, (match, arrayPath, content) => {
      const array = this.getNestedValue(context, arrayPath.trim());
      if (!Array.isArray(array)) return '';

      return array.map((item, index) => {
        const itemContext = {
          ...context,
          this: item,
          '@index': index,
          '@first': index === 0,
          '@last': index === array.length - 1,
        };
        return this.renderContent(content, itemContext);
      }).join('');
    });

    // Handle unless blocks {{#unless condition}}...{{/unless}}
    const unlessRegex = /\{\{#unless\s+([^}]+)\}\}([\s\S]*?)\{\{\/unless\}\}/g;
    result = result.replace(unlessRegex, (match, condition, content) => {
      const conditionValue = this.evaluateCondition(condition.trim(), context);
      return !conditionValue ? content : '';
    });

    return result;
  }

  async validate(template: string): Promise<boolean> {
    try {
      // Check for balanced handlebars
      const openBraces = (template.match(/\{\{/g) || []).length;
      const closeBraces = (template.match(/\}\}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        return false;
      }

      // Check for balanced block helpers
      const blockHelpers = ['if', 'unless', 'each', 'with'];
      for (const helper of blockHelpers) {
        const openBlocks = (template.match(new RegExp(`\\{\\{#${helper}\\b`, 'g')) || []).length;
        const closeBlocks = (template.match(new RegExp(`\\{\\{\\/${helper}\\}\\}`, 'g')) || []).length;
        
        if (openBlocks !== closeBlocks) {
          return false;
        }
      }

      return true;
    } catch {
      return false;
    }
  }

  extractVariables(template: string): string[] {
    const variables = new Set<string>();
    
    // Extract simple variables {{variable}}
    const simpleVarRegex = /\{\{([^#/][^}]*)\}\}/g;
    let match;
    
    while ((match = simpleVarRegex.exec(template)) !== null) {
      const variable = match[1].trim();
      if (!variable.startsWith('@') && variable !== 'this') {
        variables.add(variable.split('.')[0]); // Get root variable
      }
    }

    // Extract variables from block helpers
    const blockRegex = /\{\{#(if|unless|each|with)\s+([^}]+)\}\}/g;
    while ((match = blockRegex.exec(template)) !== null) {
      const variable = match[2].trim();
      if (!variable.startsWith('@') && variable !== 'this') {
        variables.add(variable.split('.')[0]);
      }
    }

    return Array.from(variables);
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    if (path === 'this') return obj.this;
    
    const parts = path.split('.');
    let current: any = obj;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  private evaluateCondition(condition: string, context: Record<string, unknown>): boolean {
    // Simple condition evaluation
    const value = this.getNestedValue(context, condition);
    
    if (typeof value === 'boolean') return value;
    if (typeof value === 'number') return value !== 0;
    if (typeof value === 'string') return value.length > 0;
    if (Array.isArray(value)) return value.length > 0;
    if (value === null || value === undefined) return false;
    
    return Boolean(value);
  }

  private renderContent(content: string, context: Record<string, unknown>): string {
    // Recursively render content with new context
    let result = content;
    
    const simpleVarRegex = /\{\{([^#/][^}]*)\}\}/g;
    result = result.replace(simpleVarRegex, (match, variable) => {
      const trimmedVar = variable.trim();
      const value = this.getNestedValue(context, trimmedVar);
      return value !== undefined ? String(value) : match;
    });
    
    return result;
  }
}

export class MustacheTemplateEngine implements TemplateEngine {
  async render(template: string, context: Record<string, unknown>): Promise<string> {
    let result = template;

    // Handle simple variable substitution {{variable}}
    const varRegex = /\{\{([^#^/][^}]*)\}\}/g;
    result = result.replace(varRegex, (match, variable) => {
      const trimmedVar = variable.trim();
      const value = this.getNestedValue(context, trimmedVar);
      return value !== undefined ? this.escapeHtml(String(value)) : '';
    });

    // Handle unescaped variables {{{variable}}}
    const unescapedRegex = /\{\{\{([^}]*)\}\}\}/g;
    result = result.replace(unescapedRegex, (match, variable) => {
      const trimmedVar = variable.trim();
      const value = this.getNestedValue(context, trimmedVar);
      return value !== undefined ? String(value) : '';
    });

    // Handle sections {{#section}}...{{/section}}
    const sectionRegex = /\{\{#([^}]+)\}\}([\s\S]*?)\{\{\/\1\}\}/g;
    result = result.replace(sectionRegex, (match, sectionName, content) => {
      const value = this.getNestedValue(context, sectionName.trim());
      
      if (Array.isArray(value)) {
        return value.map(item => {
          const itemContext = typeof item === 'object' ? { ...context, ...item } : { ...context, '.': item };
          return this.renderContent(content, itemContext);
        }).join('');
      } else if (value) {
        const sectionContext = typeof value === 'object' ? { ...context, ...value } : context;
        return this.renderContent(content, sectionContext);
      }
      
      return '';
    });

    // Handle inverted sections {{^section}}...{{/section}}
    const invertedRegex = /\{\{\^([^}]+)\}\}([\s\S]*?)\{\{\/\1\}\}/g;
    result = result.replace(invertedRegex, (match, sectionName, content) => {
      const value = this.getNestedValue(context, sectionName.trim());
      
      if (!value || (Array.isArray(value) && value.length === 0)) {
        return content;
      }
      
      return '';
    });

    return result;
  }

  async validate(template: string): Promise<boolean> {
    try {
      // Check for balanced mustaches
      const openBraces = (template.match(/\{\{/g) || []).length;
      const closeBraces = (template.match(/\}\}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        return false;
      }

      // Check for balanced sections
      const sectionRegex = /\{\{([#^])([^}]+)\}\}/g;
      const sections = new Map<string, number>();
      let match;
      
      while ((match = sectionRegex.exec(template)) !== null) {
        const type = match[1];
        const name = match[2].trim();
        
        if (type === '#' || type === '^') {
          sections.set(name, (sections.get(name) || 0) + 1);
        }
      }

      const closeSectionRegex = /\{\{\/([^}]+)\}\}/g;
      while ((match = closeSectionRegex.exec(template)) !== null) {
        const name = match[1].trim();
        const count = sections.get(name) || 0;
        
        if (count <= 0) {
          return false; // Closing tag without opening
        }
        
        sections.set(name, count - 1);
      }

      // Check if all sections are closed
      for (const count of sections.values()) {
        if (count !== 0) {
          return false;
        }
      }

      return true;
    } catch {
      return false;
    }
  }

  extractVariables(template: string): string[] {
    const variables = new Set<string>();
    
    // Extract simple variables
    const varRegex = /\{\{([^#^/][^}]*)\}\}/g;
    let match;
    
    while ((match = varRegex.exec(template)) !== null) {
      const variable = match[1].trim();
      if (variable !== '.') {
        variables.add(variable.split('.')[0]);
      }
    }

    // Extract variables from sections
    const sectionRegex = /\{\{[#^]([^}]+)\}\}/g;
    while ((match = sectionRegex.exec(template)) !== null) {
      const variable = match[1].trim();
      variables.add(variable.split('.')[0]);
    }

    return Array.from(variables);
  }

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    if (path === '.') return obj['.'] || obj;
    
    const parts = path.split('.');
    let current: any = obj;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  private renderContent(content: string, context: Record<string, unknown>): string {
    // This would recursively call render, but simplified for demo
    return content.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const value = this.getNestedValue(context, variable.trim());
      return value !== undefined ? String(value) : '';
    });
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}

export class EJSTemplateEngine implements TemplateEngine {
  async render(template: string, context: Record<string, unknown>): Promise<string> {
    let result = template;

    // Handle output tags <%= variable %>
    const outputRegex = /<%=\s*([^%]+)\s*%>/g;
    result = result.replace(outputRegex, (match, expression) => {
      try {
        const value = this.evaluateExpression(expression.trim(), context);
        return value !== undefined ? this.escapeHtml(String(value)) : '';
      } catch {
        return match;
      }
    });

    // Handle raw output tags <%- variable %>
    const rawOutputRegex = /<%-\s*([^%]+)\s*%>/g;
    result = result.replace(rawOutputRegex, (match, expression) => {
      try {
        const value = this.evaluateExpression(expression.trim(), context);
        return value !== undefined ? String(value) : '';
      } catch {
        return match;
      }
    });

    // Handle scriptlet tags <% code %>
    const scriptletRegex = /<%\s*([^%]+)\s*%>/g;
    result = result.replace(scriptletRegex, (match, code) => {
      // For demo purposes, we'll handle simple if statements
      if (code.trim().startsWith('if')) {
        // This would need a proper JavaScript parser in a real implementation
        return match; // Keep as-is for now
      }
      return ''; // Remove scriptlet tags
    });

    return result;
  }

  async validate(template: string): Promise<boolean> {
    try {
      // Check for balanced EJS tags
      const openTags = (template.match(/<%/g) || []).length;
      const closeTags = (template.match(/%>/g) || []).length;
      
      return openTags === closeTags;
    } catch {
      return false;
    }
  }

  extractVariables(template: string): string[] {
    const variables = new Set<string>();
    
    // Extract from output tags
    const outputRegex = /<%[=-]?\s*([^%]+)\s*%>/g;
    let match;
    
    while ((match = outputRegex.exec(template)) !== null) {
      const expression = match[1].trim();
      // Simple variable extraction (would need proper parsing for complex expressions)
      const varMatch = expression.match(/^([a-zA-Z_$][a-zA-Z0-9_$]*)/);
      if (varMatch) {
        variables.add(varMatch[1]);
      }
    }

    return Array.from(variables);
  }

  private evaluateExpression(expression: string, context: Record<string, unknown>): unknown {
    // Simple expression evaluation (would use a proper evaluator in production)
    if (expression in context) {
      return context[expression];
    }
    
    // Handle dot notation
    const parts = expression.split('.');
    let current: any = context;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
}
