{"name": "catalyst-v3", "version": "3.0.0", "description": "The Complete AI-Powered Prompt Engineering Ecosystem", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=!@catalyst/web && changeset publish"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@jest/globals": "^29.7.0", "@playwright/test": "^1.40.1", "@turbo/gen": "^1.11.2", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "eslint": "^8.55.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "packageManager": "npm@10.2.4"}