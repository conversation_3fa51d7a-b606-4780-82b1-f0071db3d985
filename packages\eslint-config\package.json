{"name": "@catalyst/eslint-config", "version": "0.0.0", "private": true, "main": "./base.js", "files": ["base.js", "next.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-turbo": "^1.11.2"}}