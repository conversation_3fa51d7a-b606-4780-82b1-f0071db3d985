import { v4 as uuidv4 } from 'uuid';
import type {
  PromptSpec,
  PromptTemplate,
  PromptChain,
  Parameter,
  TestCase,
  ValidationRule,
  ModelConfig,
  PromptType,
} from './types.js';

/**
 * Create a new PromptSpec with default values
 */
export function createPromptSpec(name: string, description?: string): PromptSpec {
  return {
    specVersion: '2.0',
    metadata: {
      name,
      description,
      version: '1.0.0',
      created: new Date(),
      updated: new Date(),
      tags: [],
    },
    templates: [],
    chains: [],
    globalConfig: {
      globalParameters: [],
      validationRules: [],
    },
  };
}

/**
 * Create a new PromptTemplate with default values
 */
export function createPromptTemplate(
  name: string,
  type: PromptType,
  template: string,
  options?: {
    description?: string;
    parameters?: Parameter[];
    modelConfig?: ModelConfig;
  }
): PromptTemplate {
  return {
    id: uuidv4(),
    name,
    description: options?.description,
    version: '1.0.0',
    type,
    template,
    parameters: options?.parameters || [],
    modelConfig: options?.modelConfig,
    validationRules: [],
    testCases: [],
    metadata: {
      created: new Date(),
      updated: new Date(),
      tags: [type],
      difficulty: 'beginner',
    },
  };
}

/**
 * Create a new PromptChain with default values
 */
export function createPromptChain(
  name: string,
  description?: string
): PromptChain {
  return {
    id: uuidv4(),
    name,
    description,
    version: '1.0.0',
    steps: [],
    globalParameters: [],
    errorHandling: {
      strategy: 'fail-fast',
    },
    metadata: {
      created: new Date(),
      updated: new Date(),
      tags: [],
    },
  };
}

/**
 * Create a new Parameter definition
 */
export function createParameter(
  name: string,
  type: Parameter['type'],
  options?: {
    description?: string;
    required?: boolean;
    default?: unknown;
    constraints?: Parameter['constraints'];
    examples?: unknown[];
  }
): Parameter {
  return {
    name,
    type,
    description: options?.description,
    required: options?.required || false,
    default: options?.default,
    constraints: options?.constraints,
    examples: options?.examples,
  };
}

/**
 * Create a new TestCase
 */
export function createTestCase(
  name: string,
  input: Record<string, unknown>,
  options?: {
    description?: string;
    expectedOutput?: unknown;
    expectedPattern?: string;
    tags?: string[];
    timeout?: number;
  }
): TestCase {
  return {
    name,
    description: options?.description,
    input,
    expectedOutput: options?.expectedOutput,
    expectedPattern: options?.expectedPattern,
    tags: options?.tags,
    timeout: options?.timeout,
  };
}

/**
 * Create a new ValidationRule
 */
export function createValidationRule(
  name: string,
  type: ValidationRule['type'],
  condition: string,
  message: string,
  severity: ValidationRule['severity'] = 'error'
): ValidationRule {
  return {
    name,
    type,
    condition,
    message,
    severity,
  };
}

/**
 * Create a new ModelConfig
 */
export function createModelConfig(
  provider: string,
  model: string,
  options?: {
    version?: string;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stopSequences?: string[];
  }
): ModelConfig {
  return {
    provider,
    model,
    version: options?.version,
    parameters: {
      temperature: options?.temperature,
      maxTokens: options?.maxTokens,
      topP: options?.topP,
      frequencyPenalty: options?.frequencyPenalty,
      presencePenalty: options?.presencePenalty,
      stopSequences: options?.stopSequences,
    },
  };
}

/**
 * Add a template to a PromptSpec
 */
export function addTemplate(spec: PromptSpec, template: PromptTemplate): PromptSpec {
  return {
    ...spec,
    templates: [...spec.templates, template],
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Add a chain to a PromptSpec
 */
export function addChain(spec: PromptSpec, chain: PromptChain): PromptSpec {
  return {
    ...spec,
    chains: [...spec.chains, chain],
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Update a template in a PromptSpec
 */
export function updateTemplate(
  spec: PromptSpec,
  templateId: string,
  updates: Partial<PromptTemplate>
): PromptSpec {
  return {
    ...spec,
    templates: spec.templates.map(template =>
      template.id === templateId
        ? {
            ...template,
            ...updates,
            id: template.id, // Ensure ID doesn't change
            metadata: {
              ...template.metadata,
              ...updates.metadata,
              updated: new Date(),
            },
          }
        : template
    ),
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Update a chain in a PromptSpec
 */
export function updateChain(
  spec: PromptSpec,
  chainId: string,
  updates: Partial<PromptChain>
): PromptSpec {
  return {
    ...spec,
    chains: spec.chains.map(chain =>
      chain.id === chainId
        ? {
            ...chain,
            ...updates,
            id: chain.id, // Ensure ID doesn't change
            metadata: {
              ...chain.metadata,
              ...updates.metadata,
              updated: new Date(),
            },
          }
        : chain
    ),
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Remove a template from a PromptSpec
 */
export function removeTemplate(spec: PromptSpec, templateId: string): PromptSpec {
  return {
    ...spec,
    templates: spec.templates.filter(template => template.id !== templateId),
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Remove a chain from a PromptSpec
 */
export function removeChain(spec: PromptSpec, chainId: string): PromptSpec {
  return {
    ...spec,
    chains: spec.chains.filter(chain => chain.id !== chainId),
    metadata: {
      ...spec.metadata,
      updated: new Date(),
    },
  };
}

/**
 * Find a template by ID
 */
export function findTemplate(spec: PromptSpec, templateId: string): PromptTemplate | undefined {
  return spec.templates.find(template => template.id === templateId);
}

/**
 * Find a chain by ID
 */
export function findChain(spec: PromptSpec, chainId: string): PromptChain | undefined {
  return spec.chains.find(chain => chain.id === chainId);
}

/**
 * Get all templates of a specific type
 */
export function getTemplatesByType(spec: PromptSpec, type: PromptType): PromptTemplate[] {
  return spec.templates.filter(template => template.type === type);
}

/**
 * Get all templates with specific tags
 */
export function getTemplatesByTags(spec: PromptSpec, tags: string[]): PromptTemplate[] {
  return spec.templates.filter(template =>
    template.metadata?.tags?.some(tag => tags.includes(tag))
  );
}

/**
 * Get all chains with specific tags
 */
export function getChainsByTags(spec: PromptSpec, tags: string[]): PromptChain[] {
  return spec.chains.filter(chain =>
    chain.metadata?.tags?.some(tag => tags.includes(tag))
  );
}

/**
 * Validate template variable usage
 */
export function validateTemplateVariables(template: PromptTemplate): {
  valid: boolean;
  unusedParameters: string[];
  undefinedVariables: string[];
} {
  const templateVars = extractTemplateVariables(template.template);
  const paramNames = template.parameters.map(p => p.name);

  const unusedParameters = paramNames.filter(param => !templateVars.includes(param));
  const undefinedVariables = templateVars.filter(variable => !paramNames.includes(variable));

  return {
    valid: unusedParameters.length === 0 && undefinedVariables.length === 0,
    unusedParameters,
    undefinedVariables,
  };
}

/**
 * Extract variables from a template string
 */
export function extractTemplateVariables(template: string): string[] {
  const variables: string[] = [];
  const regex = /\{\{([^}]+)\}\}/g;
  let match;

  while ((match = regex.exec(template)) !== null) {
    const variable = match[1].trim();
    // Remove handlebars helpers and get just the variable name
    const cleanVariable = variable.split(' ')[0].split('.')[0];
    if (!variables.includes(cleanVariable) && !cleanVariable.startsWith('#') && !cleanVariable.startsWith('/')) {
      variables.push(cleanVariable);
    }
  }

  return variables;
}

/**
 * Generate a summary of a PromptSpec
 */
export function generateSpecSummary(spec: PromptSpec): {
  name: string;
  version: string;
  templateCount: number;
  chainCount: number;
  totalSteps: number;
  templateTypes: Record<PromptType, number>;
  tags: string[];
} {
  const templateTypes = spec.templates.reduce((acc, template) => {
    acc[template.type] = (acc[template.type] || 0) + 1;
    return acc;
  }, {} as Record<PromptType, number>);

  const totalSteps = spec.chains.reduce((sum, chain) => sum + chain.steps.length, 0);

  const allTags = new Set<string>();
  spec.templates.forEach(template => {
    template.metadata?.tags?.forEach(tag => allTags.add(tag));
  });
  spec.chains.forEach(chain => {
    chain.metadata?.tags?.forEach(tag => allTags.add(tag));
  });

  return {
    name: spec.metadata.name,
    version: spec.metadata.version,
    templateCount: spec.templates.length,
    chainCount: spec.chains.length,
    totalSteps,
    templateTypes,
    tags: Array.from(allTags),
  };
}

/**
 * Clone a PromptSpec with new IDs
 */
export function cloneSpec(spec: PromptSpec, newName?: string): PromptSpec {
  const cloned: PromptSpec = {
    ...spec,
    metadata: {
      ...spec.metadata,
      name: newName || `${spec.metadata.name} (Copy)`,
      created: new Date(),
      updated: new Date(),
    },
    templates: spec.templates.map(template => ({
      ...template,
      id: uuidv4(),
      metadata: {
        ...template.metadata,
        created: new Date(),
        updated: new Date(),
      },
    })),
    chains: spec.chains.map(chain => ({
      ...chain,
      id: uuidv4(),
      steps: chain.steps.map(step => ({
        ...step,
        id: uuidv4(),
      })),
      metadata: {
        ...chain.metadata,
        created: new Date(),
        updated: new Date(),
      },
    })),
  };

  return cloned;
}
