import type { 
  OptimizationCoordinator, 
  Intent, 
  RouteConfig, 
  ProcessingResult 
} from './types.js';

export class CatalystOptimizationCoordinator implements OptimizationCoordinator {
  private optimizationStrategies: Map<string, OptimizationStrategy> = new Map();
  private performanceThresholds = {
    excellent: 0.9,
    good: 0.7,
    acceptable: 0.5,
    poor: 0.3,
  };

  constructor() {
    this.initializeOptimizationStrategies();
  }

  async optimize(intent: Intent, route: RouteConfig): Promise<Intent> {
    const strategy = this.selectOptimizationStrategy(intent, route);
    
    if (!strategy) {
      return intent; // No optimization needed
    }

    const optimizedIntent = await strategy.optimize(intent, route);
    
    // Validate the optimization
    if (this.validateOptimization(intent, optimizedIntent)) {
      return optimizedIntent;
    }

    return intent; // Return original if optimization failed validation
  }

  async suggestImprovements(result: ProcessingResult): Promise<string[]> {
    const suggestions: string[] = [];
    const performance = this.calculatePerformanceScore(result);

    // Performance-based suggestions
    if (performance < this.performanceThresholds.acceptable) {
      suggestions.push('Consider using a more capable model for this task');
      suggestions.push('Break down complex requests into smaller steps');
    }

    if (result.performance.duration > 30000) { // 30 seconds
      suggestions.push('Optimize prompt length to reduce processing time');
      suggestions.push('Consider caching similar requests');
    }

    if (result.performance.tokensUsed && result.performance.tokensUsed > 3000) {
      suggestions.push('Reduce prompt verbosity to save tokens');
      suggestions.push('Use more specific instructions to avoid lengthy responses');
    }

    // Error-based suggestions
    if (!result.success && result.error) {
      suggestions.push(...this.getErrorBasedSuggestions(result.error));
    }

    // Context-based suggestions
    suggestions.push(...this.getContextBasedSuggestions(result));

    return suggestions;
  }

  async analyzePerformance(results: ProcessingResult[]): Promise<Record<string, number>> {
    if (results.length === 0) {
      return {};
    }

    const analysis: Record<string, number> = {};

    // Overall metrics
    analysis.averagePerformance = this.calculateAveragePerformance(results);
    analysis.successRate = results.filter(r => r.success).length / results.length;
    analysis.averageDuration = results.reduce((sum, r) => sum + r.performance.duration, 0) / results.length;
    
    // Token usage analysis
    const tokenResults = results.filter(r => r.performance.tokensUsed);
    if (tokenResults.length > 0) {
      analysis.averageTokens = tokenResults.reduce((sum, r) => sum + (r.performance.tokensUsed || 0), 0) / tokenResults.length;
    }

    // Performance distribution
    analysis.excellentResults = results.filter(r => this.calculatePerformanceScore(r) >= this.performanceThresholds.excellent).length / results.length;
    analysis.goodResults = results.filter(r => this.calculatePerformanceScore(r) >= this.performanceThresholds.good).length / results.length;
    analysis.acceptableResults = results.filter(r => this.calculatePerformanceScore(r) >= this.performanceThresholds.acceptable).length / results.length;

    // Route-specific analysis
    const routeGroups = this.groupResultsByRoute(results);
    for (const [routeId, routeResults] of routeGroups) {
      analysis[`route_${routeId}_performance`] = this.calculateAveragePerformance(routeResults);
      analysis[`route_${routeId}_success_rate`] = routeResults.filter(r => r.success).length / routeResults.length;
    }

    return analysis;
  }

  // Additional utility methods
  async optimizeForLatency(intent: Intent): Promise<Intent> {
    const optimizedIntent = { ...intent };

    // Reduce prompt length while preserving meaning
    if (intent.prompt.length > 500) {
      optimizedIntent.prompt = this.compressPrompt(intent.prompt);
    }

    // Adjust model parameters for speed
    if (intent.constraints) {
      optimizedIntent.constraints = {
        ...intent.constraints,
        maxTokens: Math.min(intent.constraints.maxTokens as number || 2048, 1024),
        temperature: Math.min(intent.constraints.temperature as number || 0.7, 0.5),
      };
    }

    return optimizedIntent;
  }

  async optimizeForQuality(intent: Intent): Promise<Intent> {
    const optimizedIntent = { ...intent };

    // Enhance prompt with quality instructions
    optimizedIntent.prompt = this.enhancePromptForQuality(intent.prompt);

    // Adjust model parameters for quality
    if (intent.constraints) {
      optimizedIntent.constraints = {
        ...intent.constraints,
        temperature: 0.3, // Lower temperature for more consistent results
      };
    }

    return optimizedIntent;
  }

  private selectOptimizationStrategy(
    intent: Intent, 
    route: RouteConfig
  ): OptimizationStrategy | null {
    // Select strategy based on intent type and route characteristics
    if (intent.type === 'optimize') {
      return this.optimizationStrategies.get('quality-focused');
    }

    if (intent.priority > 7) {
      return this.optimizationStrategies.get('latency-focused');
    }

    if (route.modelConfig.provider === 'openai' && route.modelConfig.model.includes('gpt-4')) {
      return this.optimizationStrategies.get('token-efficient');
    }

    return this.optimizationStrategies.get('balanced');
  }

  private validateOptimization(original: Intent, optimized: Intent): boolean {
    // Ensure core intent is preserved
    if (original.type !== optimized.type) {
      return false;
    }

    // Ensure prompt hasn't been over-compressed
    if (optimized.prompt.length < original.prompt.length * 0.3) {
      return false;
    }

    // Ensure priority hasn't been drastically changed
    if (Math.abs(original.priority - optimized.priority) > 2) {
      return false;
    }

    return true;
  }

  private calculatePerformanceScore(result: ProcessingResult): number {
    let score = 0.5; // Base score

    // Success factor
    if (result.success) {
      score += 0.3;
    }

    // Duration factor (faster is better)
    const durationScore = Math.max(0, 1 - (result.performance.duration / 60000)); // Normalize to 1 minute
    score += durationScore * 0.2;

    return Math.min(score, 1.0);
  }

  private calculateAveragePerformance(results: ProcessingResult[]): number {
    if (results.length === 0) return 0;
    
    const totalScore = results.reduce((sum, result) => {
      return sum + this.calculatePerformanceScore(result);
    }, 0);

    return totalScore / results.length;
  }

  private getErrorBasedSuggestions(error: string): string[] {
    const suggestions: string[] = [];

    if (error.includes('timeout')) {
      suggestions.push('Reduce prompt complexity to avoid timeouts');
      suggestions.push('Consider breaking the task into smaller parts');
    }

    if (error.includes('token') || error.includes('length')) {
      suggestions.push('Shorten the input prompt');
      suggestions.push('Reduce the maximum token limit');
    }

    if (error.includes('rate limit')) {
      suggestions.push('Implement request queuing to handle rate limits');
      suggestions.push('Consider using a different model provider');
    }

    return suggestions;
  }

  private getContextBasedSuggestions(result: ProcessingResult): string[] {
    const suggestions: string[] = [];

    // Add context-specific suggestions based on metadata
    if (result.metadata?.complexity === 'high') {
      suggestions.push('Consider using a more powerful model for complex tasks');
    }

    if (result.metadata?.domain) {
      suggestions.push(`Optimize prompts for ${result.metadata.domain} domain-specific tasks`);
    }

    return suggestions;
  }

  private groupResultsByRoute(results: ProcessingResult[]): Map<string, ProcessingResult[]> {
    const groups = new Map<string, ProcessingResult[]>();

    for (const result of results) {
      const routeId = result.routeId;
      if (!groups.has(routeId)) {
        groups.set(routeId, []);
      }
      groups.get(routeId)!.push(result);
    }

    return groups;
  }

  private compressPrompt(prompt: string): string {
    // Simple prompt compression - remove redundant words and phrases
    return prompt
      .replace(/\b(please|kindly|if you could|would you mind)\b/gi, '')
      .replace(/\b(very|really|quite|extremely)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private enhancePromptForQuality(prompt: string): string {
    const qualityInstructions = [
      'Provide a comprehensive and well-structured response.',
      'Ensure accuracy and attention to detail.',
      'Include relevant examples where appropriate.',
    ];

    return `${prompt}\n\nAdditional instructions:\n${qualityInstructions.join('\n')}`;
  }

  private initializeOptimizationStrategies(): void {
    // Quality-focused strategy
    this.optimizationStrategies.set('quality-focused', {
      optimize: async (intent: Intent, route: RouteConfig) => {
        return this.optimizeForQuality(intent);
      },
    });

    // Latency-focused strategy
    this.optimizationStrategies.set('latency-focused', {
      optimize: async (intent: Intent, route: RouteConfig) => {
        return this.optimizeForLatency(intent);
      },
    });

    // Token-efficient strategy
    this.optimizationStrategies.set('token-efficient', {
      optimize: async (intent: Intent, route: RouteConfig) => {
        const optimized = { ...intent };
        optimized.prompt = this.compressPrompt(intent.prompt);
        return optimized;
      },
    });

    // Balanced strategy
    this.optimizationStrategies.set('balanced', {
      optimize: async (intent: Intent, route: RouteConfig) => {
        // Apply moderate optimizations
        const optimized = { ...intent };
        if (intent.prompt.length > 1000) {
          optimized.prompt = this.compressPrompt(intent.prompt);
        }
        return optimized;
      },
    });
  }
}

interface OptimizationStrategy {
  optimize(intent: Intent, route: RouteConfig): Promise<Intent>;
}
