import { v4 as uuidv4 } from 'uuid';
import type { IntentAnalyzer, Intent, Context, IntentType } from './types.js';

export class CatalystIntentAnalyzer implements IntentAnalyzer {
  private intentPatterns: Map<IntentType, RegExp[]> = new Map([
    ['optimize', [
      /optimize|improve|enhance|refine|better/i,
      /make.*better|more effective|more efficient/i,
    ]],
    ['generate', [
      /create|generate|build|make|produce/i,
      /write.*for me|give me.*example/i,
    ]],
    ['analyze', [
      /analyze|examine|review|evaluate|assess/i,
      /what.*think|how.*good|quality/i,
    ]],
    ['transform', [
      /transform|convert|change|modify|adapt/i,
      /turn.*into|make.*sound|rewrite/i,
    ]],
    ['chain', [
      /then|next|after|sequence|workflow/i,
      /step.*step|multi.*step|chain/i,
    ]],
    ['route', [
      /route|send|forward|delegate|assign/i,
      /which.*model|best.*for|recommend/i,
    ]],
    ['synthesize', [
      /combine|merge|synthesize|blend|fuse/i,
      /bring.*together|mix.*with/i,
    ]],
  ]);

  private requirementPatterns: Map<string, RegExp[]> = new Map([
    ['tone', [
      /tone|style|voice|manner/i,
      /formal|informal|casual|professional|friendly/i,
    ]],
    ['length', [
      /length|long|short|brief|detailed/i,
      /\d+\s*words?|\d+\s*characters?|\d+\s*sentences?/i,
    ]],
    ['format', [
      /format|structure|layout|template/i,
      /json|yaml|markdown|html|csv/i,
    ]],
    ['audience', [
      /audience|for.*who|target|readers?/i,
      /beginners?|experts?|children|adults/i,
    ]],
    ['language', [
      /language|translate|in.*spanish|in.*french/i,
      /english|spanish|french|german|chinese/i,
    ]],
  ]);

  async analyze(prompt: string, context: Context): Promise<Intent> {
    const intentType = await this.classifyIntent(prompt);
    const requirements = await this.extractRequirements(prompt);
    
    const priority = this.calculatePriority(prompt, context);
    const constraints = this.extractConstraints(prompt);

    return {
      id: uuidv4(),
      type: intentType,
      prompt,
      context,
      priority,
      requirements,
      constraints,
    };
  }

  async extractRequirements(prompt: string): Promise<Record<string, unknown>> {
    const requirements: Record<string, unknown> = {};

    for (const [requirement, patterns] of this.requirementPatterns) {
      for (const pattern of patterns) {
        const match = prompt.match(pattern);
        if (match) {
          requirements[requirement] = this.extractRequirementValue(
            requirement,
            match[0],
            prompt
          );
          break;
        }
      }
    }

    return requirements;
  }

  async classifyIntent(prompt: string): Promise<IntentType> {
    const scores: Map<IntentType, number> = new Map();

    // Initialize scores
    for (const intentType of this.intentPatterns.keys()) {
      scores.set(intentType, 0);
    }

    // Score based on pattern matching
    for (const [intentType, patterns] of this.intentPatterns) {
      let score = 0;
      for (const pattern of patterns) {
        if (pattern.test(prompt)) {
          score += 1;
        }
      }
      scores.set(intentType, score);
    }

    // Find the highest scoring intent type
    let bestIntent: IntentType = 'generate';
    let bestScore = 0;

    for (const [intentType, score] of scores) {
      if (score > bestScore) {
        bestScore = score;
        bestIntent = intentType;
      }
    }

    // If no patterns matched, use contextual analysis
    if (bestScore === 0) {
      bestIntent = this.contextualClassification(prompt);
    }

    return bestIntent;
  }

  private calculatePriority(prompt: string, context: Context): number {
    let priority = 5; // Default priority

    // Increase priority for urgent keywords
    if (/urgent|asap|immediately|critical|important/i.test(prompt)) {
      priority += 2;
    }

    // Increase priority for complex requests
    if (prompt.length > 500) {
      priority += 1;
    }

    // Adjust based on context history
    if (context.history && context.history.length > 5) {
      priority += 1; // Long conversations get higher priority
    }

    return Math.min(priority, 10);
  }

  private extractConstraints(prompt: string): Record<string, unknown> {
    const constraints: Record<string, unknown> = {};

    // Time constraints
    const timeMatch = prompt.match(/within\s+(\d+)\s*(minutes?|hours?|days?)/i);
    if (timeMatch) {
      constraints.timeLimit = {
        value: parseInt(timeMatch[1]),
        unit: timeMatch[2].toLowerCase(),
      };
    }

    // Quality constraints
    if (/high.quality|best.possible|perfect/i.test(prompt)) {
      constraints.quality = 'high';
    }

    // Model constraints
    const modelMatch = prompt.match(/use\s+(gpt|claude|gemini|mistral)/i);
    if (modelMatch) {
      constraints.preferredModel = modelMatch[1].toLowerCase();
    }

    return constraints;
  }

  private extractRequirementValue(
    requirement: string,
    match: string,
    fullPrompt: string
  ): unknown {
    switch (requirement) {
      case 'length':
        const lengthMatch = match.match(/(\d+)/);
        return lengthMatch ? parseInt(lengthMatch[1]) : 'medium';
      
      case 'tone':
        if (/formal|professional/i.test(match)) return 'formal';
        if (/casual|informal|friendly/i.test(match)) return 'casual';
        return 'neutral';
      
      case 'format':
        if (/json/i.test(match)) return 'json';
        if (/yaml/i.test(match)) return 'yaml';
        if (/markdown/i.test(match)) return 'markdown';
        return 'text';
      
      case 'audience':
        if (/beginners?|novice/i.test(match)) return 'beginner';
        if (/experts?|advanced/i.test(match)) return 'expert';
        return 'general';
      
      case 'language':
        const langMatch = match.match(/(english|spanish|french|german|chinese)/i);
        return langMatch ? langMatch[1].toLowerCase() : 'english';
      
      default:
        return match;
    }
  }

  private contextualClassification(prompt: string): IntentType {
    // Simple heuristics for classification when patterns don't match
    if (prompt.includes('?')) {
      return 'analyze';
    }
    
    if (prompt.length > 200) {
      return 'generate';
    }
    
    if (prompt.split(' ').length < 10) {
      return 'optimize';
    }
    
    return 'generate';
  }
}
