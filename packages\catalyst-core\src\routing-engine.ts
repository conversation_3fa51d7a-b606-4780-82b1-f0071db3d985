import { cloneDeep } from 'lodash';
import type { RoutingEngine, RouteConfig, Intent, IntentType } from './types.js';

export class CatalystRoutingEngine implements RoutingEngine {
  private routes: Map<string, RouteConfig> = new Map();
  private routeWeights: Map<string, number> = new Map();
  private routePerformance: Map<string, number[]> = new Map();

  constructor() {
    this.initializeDefaultRoutes();
  }

  addRoute(config: RouteConfig): void {
    this.routes.set(config.id, cloneDeep(config));
    this.routeWeights.set(config.id, 1.0); // Default weight
    this.routePerformance.set(config.id, []);
  }

  removeRoute(routeId: string): void {
    this.routes.delete(routeId);
    this.routeWeights.delete(routeId);
    this.routePerformance.delete(routeId);
  }

  async findBestRoute(intent: Intent): Promise<RouteConfig | null> {
    const candidateRoutes = this.findCandidateRoutes(intent);
    
    if (candidateRoutes.length === 0) {
      return this.getFallbackRoute(intent);
    }

    // Score routes based on multiple factors
    const scoredRoutes = candidateRoutes.map(route => ({
      route,
      score: this.calculateRouteScore(route, intent),
    }));

    // Sort by score (highest first)
    scoredRoutes.sort((a, b) => b.score - a.score);

    return scoredRoutes[0].route;
  }

  getAllRoutes(): RouteConfig[] {
    return Array.from(this.routes.values());
  }

  updateRoute(routeId: string, updates: Partial<RouteConfig>): void {
    const existingRoute = this.routes.get(routeId);
    if (!existingRoute) {
      throw new Error(`Route with id ${routeId} not found`);
    }

    const updatedRoute: RouteConfig = {
      ...existingRoute,
      ...updates,
      id: routeId, // Ensure ID cannot be changed
    };

    this.routes.set(routeId, updatedRoute);
  }

  // Additional methods for route management
  updateRouteWeight(routeId: string, weight: number): void {
    if (weight < 0 || weight > 2) {
      throw new Error('Route weight must be between 0 and 2');
    }
    this.routeWeights.set(routeId, weight);
  }

  recordRoutePerformance(routeId: string, performance: number): void {
    const performances = this.routePerformance.get(routeId) || [];
    performances.push(performance);
    
    // Keep only the last 100 performance records
    if (performances.length > 100) {
      performances.shift();
    }
    
    this.routePerformance.set(routeId, performances);
  }

  getRouteStats(routeId: string): {
    averagePerformance: number;
    totalRequests: number;
    weight: number;
  } | null {
    const performances = this.routePerformance.get(routeId);
    const weight = this.routeWeights.get(routeId);
    
    if (!performances || !weight) {
      return null;
    }

    const averagePerformance = performances.length > 0
      ? performances.reduce((sum, perf) => sum + perf, 0) / performances.length
      : 0;

    return {
      averagePerformance,
      totalRequests: performances.length,
      weight,
    };
  }

  private findCandidateRoutes(intent: Intent): RouteConfig[] {
    const candidates: RouteConfig[] = [];

    for (const route of this.routes.values()) {
      if (this.isRouteCompatible(route, intent)) {
        candidates.push(route);
      }
    }

    return candidates;
  }

  private isRouteCompatible(route: RouteConfig, intent: Intent): boolean {
    // Check if route supports the intent type
    if (!route.intentTypes.includes(intent.type)) {
      return false;
    }

    // Check conditions if they exist
    if (route.conditions) {
      return this.evaluateConditions(route.conditions, intent);
    }

    return true;
  }

  private evaluateConditions(
    conditions: Record<string, unknown>,
    intent: Intent
  ): boolean {
    for (const [key, expectedValue] of Object.entries(conditions)) {
      const actualValue = this.getIntentProperty(intent, key);
      
      if (!this.matchesCondition(actualValue, expectedValue)) {
        return false;
      }
    }

    return true;
  }

  private getIntentProperty(intent: Intent, propertyPath: string): unknown {
    const parts = propertyPath.split('.');
    let current: any = intent;

    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }

    return current;
  }

  private matchesCondition(actual: unknown, expected: unknown): boolean {
    if (typeof expected === 'object' && expected !== null) {
      const condition = expected as Record<string, unknown>;
      
      if ('$gt' in condition) {
        return typeof actual === 'number' && actual > (condition.$gt as number);
      }
      
      if ('$lt' in condition) {
        return typeof actual === 'number' && actual < (condition.$lt as number);
      }
      
      if ('$in' in condition) {
        return Array.isArray(condition.$in) && condition.$in.includes(actual);
      }
      
      if ('$regex' in condition) {
        return typeof actual === 'string' && 
               new RegExp(condition.$regex as string).test(actual);
      }
    }

    return actual === expected;
  }

  private calculateRouteScore(route: RouteConfig, intent: Intent): number {
    let score = route.priority; // Base score from route priority

    // Apply route weight
    const weight = this.routeWeights.get(route.id) || 1.0;
    score *= weight;

    // Consider historical performance
    const performances = this.routePerformance.get(route.id);
    if (performances && performances.length > 0) {
      const avgPerformance = performances.reduce((sum, perf) => sum + perf, 0) / performances.length;
      score *= (0.5 + avgPerformance); // Performance factor between 0.5 and 1.5
    }

    // Boost score for exact intent type matches
    if (route.intentTypes.length === 1 && route.intentTypes[0] === intent.type) {
      score *= 1.2;
    }

    // Consider intent priority
    score += intent.priority * 0.1;

    return score;
  }

  private getFallbackRoute(intent: Intent): RouteConfig | null {
    // Look for routes with fallback capability
    for (const route of this.routes.values()) {
      if (route.fallback && route.intentTypes.includes('generate')) {
        return route;
      }
    }

    // Return the first general-purpose route
    for (const route of this.routes.values()) {
      if (route.intentTypes.length > 3) {
        return route;
      }
    }

    return null;
  }

  private initializeDefaultRoutes(): void {
    // GPT-4o route for complex generation and analysis
    this.addRoute({
      id: 'gpt-4o-primary',
      name: 'GPT-4o Primary',
      description: 'High-quality generation and analysis with GPT-4o',
      intentTypes: ['generate', 'analyze', 'optimize'],
      modelConfig: {
        provider: 'openai',
        model: 'gpt-4o',
        maxTokens: 4096,
        temperature: 0.7,
      },
      priority: 8,
    });

    // Claude 3.5 Sonnet for reasoning and transformation
    this.addRoute({
      id: 'claude-3-5-sonnet',
      name: 'Claude 3.5 Sonnet',
      description: 'Advanced reasoning and transformation with Claude',
      intentTypes: ['analyze', 'transform', 'synthesize'],
      modelConfig: {
        provider: 'anthropic',
        model: 'claude-3-5-sonnet-20241022',
        maxTokens: 4096,
        temperature: 0.7,
      },
      priority: 8,
    });

    // Gemini Pro for multimodal and chain operations
    this.addRoute({
      id: 'gemini-pro',
      name: 'Gemini Pro',
      description: 'Multimodal processing and chain operations',
      intentTypes: ['chain', 'route', 'synthesize'],
      modelConfig: {
        provider: 'google',
        model: 'gemini-pro',
        maxTokens: 2048,
        temperature: 0.8,
      },
      priority: 7,
    });

    // Mistral Large for optimization and routing
    this.addRoute({
      id: 'mistral-large',
      name: 'Mistral Large',
      description: 'Fast optimization and routing decisions',
      intentTypes: ['optimize', 'route'],
      modelConfig: {
        provider: 'mistral',
        model: 'mistral-large-latest',
        maxTokens: 2048,
        temperature: 0.6,
      },
      priority: 6,
    });
  }
}
