// Main exports
export { CatalystPromptSpecCompiler } from './compiler.js';

// Template engine exports
export {
  HandlebarsTemplateEngine,
  MustacheTemplateEngine,
  EJSTemplateEngine,
} from './template-engine.js';

// Utility function exports
export {
  createPromptSpec,
  createPromptTemplate,
  createPrompt<PERSON>hai<PERSON>,
  createParameter,
  createTestCase,
  createValidationRule,
  createModelConfig,
  addTemplate,
  addChain,
  updateTemplate,
  updateChain,
  removeTemplate,
  removeChain,
  findTemplate,
  findChain,
  getTemplatesByType,
  getTemplatesByTags,
  getChainsByTags,
  validateTemplateVariables,
  extractTemplateVariables,
  generateSpecSummary,
  cloneSpec,
} from './utils.js';

// Type exports
export type {
  // Core types
  PromptSpecVersion,
  PromptType,
  OutputFormat,
  Parameter,
  ModelConfig,
  ValidationRule,
  TestCase,
  PromptTemplate,
  ChainStep,
  PromptChain,
  PromptSpec,
  CompilationOptions,
  CompilationResult,
  
  // Interface types
  PromptSpecCompiler,
  TemplateEngine,
  ValidationResult,
  
  // Utility types
  PromptSpecElement,
  CompilerError,
  SpecMetadata,
} from './types.js';

// Schema exports for validation
export {
  PromptSpecVersionSchema,
  PromptTypeSchema,
  OutputFormatSchema,
  ParameterSchema,
  ModelConfigSchema,
  ValidationRuleSchema,
  TestCaseSchema,
  PromptTemplateSchema,
  ChainStepSchema,
  PromptChainSchema,
  PromptSpecSchema,
  CompilationOptionsSchema,
  CompilationResultSchema,
} from './types.js';

// Factory functions
export const createCompiler = () => new CatalystPromptSpecCompiler();

export const createHandlebarsEngine = () => new HandlebarsTemplateEngine();
export const createMustacheEngine = () => new MustacheTemplateEngine();
export const createEJSEngine = () => new EJSTemplateEngine();

// Common model configurations
export const commonModelConfigs = {
  gpt4o: createModelConfig('openai', 'gpt-4o', {
    temperature: 0.7,
    maxTokens: 4096,
  }),
  claude35Sonnet: createModelConfig('anthropic', 'claude-3-5-sonnet-20241022', {
    temperature: 0.7,
    maxTokens: 4096,
  }),
  geminiPro: createModelConfig('google', 'gemini-pro', {
    temperature: 0.8,
    maxTokens: 2048,
  }),
  mistralLarge: createModelConfig('mistral', 'mistral-large-latest', {
    temperature: 0.6,
    maxTokens: 2048,
  }),
};

// Common validation rules
export const commonValidationRules = {
  minLength: (minChars: number) => createValidationRule(
    'min-length',
    'length',
    `length >= ${minChars}`,
    `Content must be at least ${minChars} characters long`
  ),
  maxLength: (maxChars: number) => createValidationRule(
    'max-length',
    'length',
    `length <= ${maxChars}`,
    `Content must not exceed ${maxChars} characters`
  ),
  noEmptyContent: () => createValidationRule(
    'no-empty',
    'content',
    'trim().length > 0',
    'Content cannot be empty'
  ),
  validEmail: () => createValidationRule(
    'valid-email',
    'format',
    '/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value)',
    'Must be a valid email address'
  ),
  validUrl: () => createValidationRule(
    'valid-url',
    'format',
    '/^https?:\\/\\/.+/.test(value)',
    'Must be a valid HTTP/HTTPS URL'
  ),
};

// Template presets
export const templatePresets = {
  systemPrompt: (purpose: string) => createPromptTemplate(
    'System Prompt',
    'system',
    `You are a helpful AI assistant specialized in ${purpose}. Please provide accurate, helpful, and relevant responses to user queries.`,
    {
      description: `System prompt for ${purpose} assistance`,
      parameters: [
        createParameter('purpose', 'string', {
          description: 'The specific purpose or domain of assistance',
          required: true,
          default: purpose,
        }),
      ],
    }
  ),
  
  userQuery: () => createPromptTemplate(
    'User Query',
    'user',
    'I need help with {{task}}. Here are the details: {{details}}',
    {
      description: 'Standard user query template',
      parameters: [
        createParameter('task', 'string', {
          description: 'The task the user needs help with',
          required: true,
        }),
        createParameter('details', 'string', {
          description: 'Additional details about the task',
          required: false,
        }),
      ],
    }
  ),
  
  codeGeneration: () => createPromptTemplate(
    'Code Generation',
    'user',
    'Generate {{language}} code for the following requirement:\n\n{{requirement}}\n\nPlease include:\n- Clear comments\n- Error handling\n- Best practices\n\nCode:',
    {
      description: 'Template for code generation requests',
      parameters: [
        createParameter('language', 'string', {
          description: 'Programming language',
          required: true,
          examples: ['Python', 'JavaScript', 'TypeScript', 'Java'],
        }),
        createParameter('requirement', 'string', {
          description: 'The code requirement or specification',
          required: true,
        }),
      ],
    }
  ),
  
  textAnalysis: () => createPromptTemplate(
    'Text Analysis',
    'user',
    'Please analyze the following text for {{analysisType}}:\n\n{{text}}\n\nProvide a detailed analysis including:\n- Key findings\n- Insights\n- Recommendations',
    {
      description: 'Template for text analysis tasks',
      parameters: [
        createParameter('analysisType', 'string', {
          description: 'Type of analysis to perform',
          required: true,
          examples: ['sentiment', 'themes', 'readability', 'bias'],
        }),
        createParameter('text', 'string', {
          description: 'The text to analyze',
          required: true,
        }),
      ],
    }
  ),
};

// Version
export const VERSION = '0.0.0';
