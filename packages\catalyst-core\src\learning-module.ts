import type { 
  LearningModule, 
  LearningData, 
  IntentType 
} from './types.js';

export class CatalystLearningModule implements LearningModule {
  private learningData: LearningData[] = [];
  private routeWeights: Map<string, number> = new Map();
  private intentPatterns: Map<IntentType, PatternData> = new Map();
  private maxDataPoints = 10000; // Maximum learning data points to keep

  constructor(options?: { maxDataPoints?: number }) {
    if (options?.maxDataPoints) {
      this.maxDataPoints = options.maxDataPoints;
    }
    this.initializePatternTracking();
  }

  async learn(data: LearningData): Promise<void> {
    // Add the learning data
    this.learningData.push(data);

    // Trim data if we exceed the maximum
    if (this.learningData.length > this.maxDataPoints) {
      this.learningData = this.learningData.slice(-this.maxDataPoints);
    }

    // Update route weights based on performance
    await this.updateRouteWeights(data.routeId, data.performance);

    // Update intent patterns
    this.updateIntentPatterns(data);

    // Trigger adaptive learning
    this.adaptiveLearn(data);
  }

  async getInsights(intentType: IntentType): Promise<Record<string, unknown>> {
    const relevantData = this.learningData.filter(d => {
      // We need to infer intent type from the data
      // This is a simplified approach - in practice, you'd store intent type in learning data
      return true; // For now, include all data
    });

    if (relevantData.length === 0) {
      return {
        message: 'No learning data available for this intent type',
        recommendations: [],
      };
    }

    const insights: Record<string, unknown> = {};

    // Performance insights
    const performances = relevantData.map(d => d.performance);
    insights.averagePerformance = performances.reduce((sum, p) => sum + p, 0) / performances.length;
    insights.bestPerformance = Math.max(...performances);
    insights.worstPerformance = Math.min(...performances);

    // Route insights
    const routePerformance = this.analyzeRoutePerformance(relevantData);
    insights.bestRoutes = Object.entries(routePerformance)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([routeId, performance]) => ({ routeId, performance }));

    // Pattern insights
    const patternData = this.intentPatterns.get(intentType);
    if (patternData) {
      insights.commonPatterns = patternData.successfulPatterns.slice(0, 5);
      insights.problematicPatterns = patternData.problematicPatterns.slice(0, 3);
    }

    // Recommendations
    insights.recommendations = this.generateRecommendations(intentType, relevantData);

    // Temporal insights
    insights.temporalTrends = this.analyzeTemporalTrends(relevantData);

    return insights;
  }

  async updateRouteWeights(routeId: string, performance: number): Promise<void> {
    const currentWeight = this.routeWeights.get(routeId) || 1.0;
    
    // Adaptive weight adjustment based on performance
    let newWeight = currentWeight;
    
    if (performance > 0.8) {
      newWeight = Math.min(currentWeight * 1.05, 2.0); // Increase weight for good performance
    } else if (performance < 0.4) {
      newWeight = Math.max(currentWeight * 0.95, 0.1); // Decrease weight for poor performance
    }

    this.routeWeights.set(routeId, newWeight);
  }

  async exportLearningData(): Promise<LearningData[]> {
    return [...this.learningData]; // Return a copy
  }

  // Additional utility methods
  async importLearningData(data: LearningData[]): Promise<void> {
    // Merge with existing data, avoiding duplicates
    const existingIds = new Set(this.learningData.map(d => d.intentId));
    const newData = data.filter(d => !existingIds.has(d.intentId));
    
    this.learningData.push(...newData);
    
    // Trim if necessary
    if (this.learningData.length > this.maxDataPoints) {
      this.learningData = this.learningData.slice(-this.maxDataPoints);
    }

    // Reprocess all data for patterns and weights
    await this.reprocessLearningData();
  }

  async clearLearningData(): Promise<void> {
    this.learningData = [];
    this.routeWeights.clear();
    this.initializePatternTracking();
  }

  getRouteWeight(routeId: string): number {
    return this.routeWeights.get(routeId) || 1.0;
  }

  getLearningStats(): {
    totalDataPoints: number;
    averagePerformance: number;
    routeCount: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    const totalDataPoints = this.learningData.length;
    
    if (totalDataPoints === 0) {
      return {
        totalDataPoints: 0,
        averagePerformance: 0,
        routeCount: 0,
        oldestEntry: null,
        newestEntry: null,
      };
    }

    const averagePerformance = this.learningData.reduce((sum, d) => sum + d.performance, 0) / totalDataPoints;
    const routeCount = new Set(this.learningData.map(d => d.routeId)).size;
    
    const timestamps = this.learningData.map(d => d.timestamp);
    const oldestEntry = new Date(Math.min(...timestamps.map(t => t.getTime())));
    const newestEntry = new Date(Math.max(...timestamps.map(t => t.getTime())));

    return {
      totalDataPoints,
      averagePerformance,
      routeCount,
      oldestEntry,
      newestEntry,
    };
  }

  private updateIntentPatterns(data: LearningData): void {
    // This is a simplified pattern tracking
    // In practice, you'd analyze the actual prompts and responses
    const intentType = 'generate' as IntentType; // Placeholder - would be derived from data
    
    let patternData = this.intentPatterns.get(intentType);
    if (!patternData) {
      patternData = {
        successfulPatterns: [],
        problematicPatterns: [],
        totalSamples: 0,
      };
      this.intentPatterns.set(intentType, patternData);
    }

    patternData.totalSamples++;

    // Track patterns based on performance
    if (data.performance > 0.7) {
      // This would analyze the actual prompt for patterns
      patternData.successfulPatterns.push(`Pattern from ${data.intentId}`);
    } else if (data.performance < 0.4) {
      patternData.problematicPatterns.push(`Problem pattern from ${data.intentId}`);
    }

    // Keep only recent patterns
    patternData.successfulPatterns = patternData.successfulPatterns.slice(-50);
    patternData.problematicPatterns = patternData.problematicPatterns.slice(-20);
  }

  private adaptiveLearn(data: LearningData): void {
    // Implement adaptive learning algorithms
    // This could include reinforcement learning, pattern recognition, etc.
    
    // Simple adaptive approach: adjust route preferences based on recent performance
    const recentData = this.learningData
      .filter(d => d.routeId === data.routeId)
      .slice(-10); // Last 10 data points for this route

    if (recentData.length >= 5) {
      const recentPerformance = recentData.reduce((sum, d) => sum + d.performance, 0) / recentData.length;
      
      // If recent performance is consistently good or bad, adjust weight more aggressively
      if (recentPerformance > 0.8) {
        const currentWeight = this.routeWeights.get(data.routeId) || 1.0;
        this.routeWeights.set(data.routeId, Math.min(currentWeight * 1.1, 2.0));
      } else if (recentPerformance < 0.3) {
        const currentWeight = this.routeWeights.get(data.routeId) || 1.0;
        this.routeWeights.set(data.routeId, Math.max(currentWeight * 0.9, 0.1));
      }
    }
  }

  private analyzeRoutePerformance(data: LearningData[]): Record<string, number> {
    const routePerformance: Record<string, number[]> = {};

    // Group performance by route
    for (const item of data) {
      if (!routePerformance[item.routeId]) {
        routePerformance[item.routeId] = [];
      }
      routePerformance[item.routeId].push(item.performance);
    }

    // Calculate average performance for each route
    const averages: Record<string, number> = {};
    for (const [routeId, performances] of Object.entries(routePerformance)) {
      averages[routeId] = performances.reduce((sum, p) => sum + p, 0) / performances.length;
    }

    return averages;
  }

  private generateRecommendations(intentType: IntentType, data: LearningData[]): string[] {
    const recommendations: string[] = [];

    // Analyze performance trends
    const averagePerformance = data.reduce((sum, d) => sum + d.performance, 0) / data.length;

    if (averagePerformance < 0.5) {
      recommendations.push('Consider using more capable models for this intent type');
      recommendations.push('Review and optimize prompts for better results');
    }

    // Route-specific recommendations
    const routePerformance = this.analyzeRoutePerformance(data);
    const bestRoute = Object.entries(routePerformance)
      .sort(([, a], [, b]) => b - a)[0];

    if (bestRoute) {
      recommendations.push(`Consider prioritizing route ${bestRoute[0]} for ${intentType} tasks`);
    }

    // Pattern-based recommendations
    const patternData = this.intentPatterns.get(intentType);
    if (patternData && patternData.problematicPatterns.length > 0) {
      recommendations.push('Avoid patterns that have shown poor performance');
    }

    return recommendations;
  }

  private analyzeTemporalTrends(data: LearningData[]): Record<string, unknown> {
    if (data.length < 10) {
      return { message: 'Insufficient data for temporal analysis' };
    }

    // Sort by timestamp
    const sortedData = [...data].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // Analyze performance over time
    const timeWindows = this.createTimeWindows(sortedData);
    const trends: Record<string, unknown> = {};

    trends.performanceOverTime = timeWindows.map(window => ({
      period: window.period,
      averagePerformance: window.data.reduce((sum, d) => sum + d.performance, 0) / window.data.length,
      sampleCount: window.data.length,
    }));

    // Detect trends
    const performances = timeWindows.map(w => 
      w.data.reduce((sum, d) => sum + d.performance, 0) / w.data.length
    );

    if (performances.length >= 3) {
      const isImproving = performances[performances.length - 1] > performances[0];
      trends.overallTrend = isImproving ? 'improving' : 'declining';
    }

    return trends;
  }

  private createTimeWindows(sortedData: LearningData[]): Array<{ period: string; data: LearningData[] }> {
    // Create weekly time windows
    const windows: Array<{ period: string; data: LearningData[] }> = [];
    const oneWeek = 7 * 24 * 60 * 60 * 1000;

    if (sortedData.length === 0) return windows;

    const startTime = sortedData[0].timestamp.getTime();
    const endTime = sortedData[sortedData.length - 1].timestamp.getTime();

    for (let time = startTime; time <= endTime; time += oneWeek) {
      const windowEnd = time + oneWeek;
      const windowData = sortedData.filter(d => {
        const dataTime = d.timestamp.getTime();
        return dataTime >= time && dataTime < windowEnd;
      });

      if (windowData.length > 0) {
        windows.push({
          period: new Date(time).toISOString().split('T')[0],
          data: windowData,
        });
      }
    }

    return windows;
  }

  private async reprocessLearningData(): Promise<void> {
    // Clear existing patterns and weights
    this.routeWeights.clear();
    this.initializePatternTracking();

    // Reprocess all data
    for (const data of this.learningData) {
      await this.updateRouteWeights(data.routeId, data.performance);
      this.updateIntentPatterns(data);
    }
  }

  private initializePatternTracking(): void {
    const intentTypes: IntentType[] = ['optimize', 'generate', 'analyze', 'transform', 'chain', 'route', 'synthesize'];
    
    for (const intentType of intentTypes) {
      this.intentPatterns.set(intentType, {
        successfulPatterns: [],
        problematicPatterns: [],
        totalSamples: 0,
      });
    }
  }
}

interface PatternData {
  successfulPatterns: string[];
  problematicPatterns: string[];
  totalSamples: number;
}
