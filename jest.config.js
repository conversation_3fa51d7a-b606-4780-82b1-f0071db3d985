/** @type {import('jest').Config} */
module.exports = {
  projects: [
    {
      displayName: 'packages',
      testMatch: ['<rootDir>/packages/*/src/**/*.test.{js,ts,tsx}'],
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: 'tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@catalyst/(.*)$': '<rootDir>/packages/$1/src',
      },
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      testEnvironment: 'node',
      collectCoverageFrom: [
        'packages/*/src/**/*.{ts,tsx}',
        '!packages/*/src/**/*.d.ts',
        '!packages/*/src/**/*.test.{ts,tsx}',
        '!packages/*/src/**/__tests__/**',
      ],
      coverageThreshold: {
        global: {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
      },
    },
    {
      displayName: 'apps/gateway',
      testMatch: ['<rootDir>/apps/gateway/**/*.test.{js,ts}'],
      transform: {
        '^.+\\.(ts)$': ['ts-jest', {
          tsconfig: 'apps/gateway/tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@catalyst/(.*)$': '<rootDir>/packages/$1/src',
      },
      setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
      testEnvironment: 'node',
    },
  ],
  collectCoverageFrom: [
    'packages/*/src/**/*.{ts,tsx}',
    'apps/gateway/src/**/*.{ts}',
    '!**/*.d.ts',
    '!**/*.test.{ts,tsx}',
    '!**/__tests__/**',
  ],
};
