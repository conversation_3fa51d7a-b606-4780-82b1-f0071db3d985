{"name": "@catalyst/promptspec", "version": "0.0.0", "description": "PromptSpec - Specification compiler for prompt engineering workflows", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist/**"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.4", "yaml": "^2.3.4", "uuid": "^9.0.1", "lodash": "^4.17.21"}, "devDependencies": {"@catalyst/eslint-config": "workspace:*", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "eslint": "^8.55.0", "jest": "^29.7.0", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "publishConfig": {"access": "restricted"}}